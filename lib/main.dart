import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:no_context_navigation/no_context_navigation.dart';

import 'src/DAPackagesRef.dart';
import 'src/app_config/app_config.dart';
import 'src/controllers/agentes_sucursales_cte_controller.dart';
import 'src/controllers/articles_icon_controller.dart';
import 'src/controllers/buscador_controller.dart';
import 'src/controllers/citas_controller.dart';
import 'src/controllers/cliente_controller.dart';
import 'src/controllers/cobro_controller.dart';
import 'src/controllers/juego_controller.dart';
import 'src/controllers/lista_precios_controller.dart';
import 'src/controllers/opciones_controller.dart';
import 'src/controllers/pedidos_offline_controller.dart';
import 'src/controllers/sucursales_agente_controller.dart';
import 'src/controllers/sync_changes_controller.dart';
import 'src/controllers/sync_controller.dart';
import 'src/controllers/ticket_controller.dart';
import 'src/controllers/user_config_controller.dart';
import 'src/controllers/dipath_controller.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final session = DASessionProvider();
  await session.initPrefs();
  await session.loadSession();
  runApp(MyApp());
  configLoading();
  initControllers();
}

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.circle
    ..loadingStyle = EasyLoadingStyle.light
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..maskType = EasyLoadingMaskType.black
    ..userInteractions = false
    ..dismissOnTap = false;
}

// Init lazy getx controllers
void initControllers() {
  Get.lazyPut(() => ArticleIconController());
  Get.lazyPut(() => BuscadorController());
  Get.lazyPut(() => CitasController());
  Get.lazyPut(() => ClienteController());
  Get.lazyPut(() => CobroController());
  Get.lazyPut(() => JuegoController());
  Get.lazyPut(() => ListaPreciosController());
  Get.lazyPut(() => OpcionesController());
  Get.lazyPut(() => PedidosOfflineController());
  Get.lazyPut(() => SyncChangesController());
  Get.lazyPut(() => SyncController());
  Get.lazyPut(() => TicketController());
  Get.lazyPut(() => UserConfigController());
  Get.lazyPut(() => DipathController());
  Get.lazyPut(() => SucursalesAgenteController());
  Get.lazyPut(() => AgentesSucursalesCteController());
}

// ignore: must_be_immutable
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    String _initial = (DASessionProvider.toLogin) ? 'home' : 'login';

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.white,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.grey[900],
      ),
    );

    Color _primaryColor = AppConfig.options.primaryColor;
    Color _accentColor = (AppConfig.options.accentColor) ??
        Theme.of(context).colorScheme.secondary;

    Widget app = MaterialApp(
      debugShowCheckedModeBanner: false,
      title: AppConfig.appName,
      initialRoute: _initial,
      navigatorKey: NavigationService.navigationKey,
      onGenerateRoute: (RouteSettings settings) =>
          AppRoutes.onGenerateRoute(context, settings),
      routes: {
        'login': (BuildContext context) => DALoginPageAutoSave(
              appName: AppConfig.appName,
              licence: AppConfig.licence,
              idUsuarioTipo: AppConfig.idUsuarioTipo,
              loginLogo: AppConfig.loginLogo,
            ),
        'home': (BuildContext context) => HomePage(),
      },
      theme: ThemeData(
        appBarTheme: AppBarTheme(
          systemOverlayStyle: SystemUiOverlayStyle.dark, // 2
        ),
        primaryColor: _primaryColor,
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: _primaryColor,
          selectionColor: _primaryColor,
          selectionHandleColor: _primaryColor,
        ),
        colorScheme:
            ColorScheme.light(primary: _primaryColor, secondary: _accentColor)
                .copyWith(surface: Colors.grey[900]),
        useMaterial3: false,
      ),
      builder: EasyLoading.init(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('es', ''), // Spanish, no country code
      ],
    );

    return DAMainProvider.multiProvider(app);
  }
}
