import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:no_context_navigation/no_context_navigation.dart';
import 'package:pedidosdipath/src/controllers/pedidos_offline_controller.dart';
import 'package:pedidosdipath/src/helpers.dart';
import 'package:pedidosdipath/src/models/pedidoOffline.dart';

import '../DAPackagesRef.dart';
import '../app_modules/pedidos_movil/app_DAAgenda.dart';
import '../controllers/juego_controller.dart';
import '../controllers/opciones_controller.dart';
import '../controllers/ticket_controller.dart';
import '../controllers/sync_changes_controller.dart';
import '../controllers/sync_controller.dart';
import '../controllers/user_config_controller.dart';
import '../prefs_manager.dart';
export '../../src/home_page.dart';

import '../widgets/app_version.dart';
import 'api_request.dart';
export 'app_routes.dart';

DASessionProvider prov = DASessionProvider();

// Share session in other pages

class AppConfig {
  static AppConfig _instancia = new AppConfig._internal();
  AppConfig._internal();

  static DAAppConfigModel get options {
    DAAppConfigModel _appConfig = DAAppConfigModel(
      licence: 'App_PedidosMovil',
      appName: 'Pedidos Dipath',
      homeLayout: '/ListPage', // Don't forget config homeLayoutArgs
      idUsuarioTipo: 1,
      primaryColor: Color(0xFF0ABCDE),
      accentColor: Color(0xFF00DDCB),
      logo: Image.asset('assets/app/app-icon.png'),
      loginLogo: Image.asset('assets/app/login.png'),
      logoAvatar: Image.asset('assets/app/app-icon.png'),
      // minLogo: SvgPicture.asset('assets/icon/box.svg'),
      // appBarLogo: SvgPicture.asset('assets/icon/box.svg', height: 20.0),
      fontName: 'BalooTamma',
      isOffline: true,
    );

    return _appConfig;
  }

  static Object homeLayoutArgs(BuildContext context) {
    return AppDAAgenda.index(context);
  }

  factory AppConfig() {
    return _instancia;
  }

  static get licence {
    String _licence = AppConfig.options.licence;
    return _licence;
  }

  static get appName {
    String _appName = AppConfig.options.appName;
    return _appName;
  }

  static get idUsuarioTipo {
    int _idUsuarioTipo = AppConfig.options.idUsuarioTipo;
    return _idUsuarioTipo;
  }

  static get homeLayout {
    return AppConfig.options.homeLayout;
  }

  static get logo {
    Widget _logo = SizedBox(
      child: AppConfig.options.logo,
      height: 150.0,
      width: 200.0,
    );
    return _logo;
  }

  static get loginLogo {
    Widget logo = SizedBox(
      child: AppConfig.options.loginLogo,
      height: 150.0,
      width: 200.0,
    );
    return logo;
  }

  static get logoAvatar {
    return AppConfig.options.logoAvatar;
  }

  static get minLogo {
    Widget _minLogo = SizedBox(
      child: AppConfig.options.minLogo,
      height: 25.0,
      width: 100.0,
    );
    return _minLogo;
  }

  static get isOffline => AppConfig.options.isOffline;

  // ignore: must_be_immutable
  static Widget appBarLogo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.0),
      child: AppConfig.options.appBarLogo,
    );
  }

  static get theme {
    ThemeData _theme = ThemeData(
      appBarTheme: AppBarTheme(
        systemOverlayStyle: SystemUiOverlayStyle.dark, // 2
      ),
      fontFamily: AppConfig.options.fontName,
      primaryColor: AppConfig.options.primaryColor,
      colorScheme: ColorScheme.light(
        primary: AppConfig.options.primaryColor,
        secondary:
            (AppConfig.options.accentColor) ?? AppConfig.options.primaryColor,
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: AppConfig.options.primaryColor,
        selectionColor: AppConfig.options.primaryColor,
        selectionHandleColor: AppConfig.options.primaryColor,
      ),
    );

    return _theme;
  }

  static get configBrightness {
    SystemUiOverlayStyle _style = SystemUiOverlayStyle.dark.copyWith(
      statusBarColor: Color.fromRGBO(0, 0, 0, 0.25),
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.grey[900],
      systemNavigationBarIconBrightness: Brightness.light,
    );

    return _style;
  }

  static sync() async {
    bool result = await InternetConnectionChecker().hasConnection;

    if (!result) {
      EasyLoading.showError(
          'No es posible sincronizar. No se cuenta con conexión a internet.');
      return;
    }

    final pedidosController = await Get.find<PedidosOfflineController>();

    if (pedidosController.pedidosOffline.isNotEmpty) {
      EasyLoading.show(status: 'Sincronizando Pedidos Offline...');
      final pedidos = pedidosController.pedidosOffline;
      int totalPedidos = pedidos.length;
      int syncedPedidos = 0;

      for (int i = 0; i < pedidos.length; i++) {
        PedidoOffline pedido = pedidos[i];
        DARequestModel _reqCart = ApiRequest.pedido(
            cliente: pedido.cliente,
            idCita: pedido.idCita.toString(),
            listaPrecios: pedido.listaPrecios,
            condicionPago: pedido.condicionPago,
            lat: pedido.mapaLatitud?.toDouble() ?? 0.0,
            lng: pedido.mapaLongitud?.toDouble() ?? 0.0,
            carrito: pedido.detalle.map((item) => item.toJson()).toList(),
            enviarA: pedido.enviarA,
            observaciones: pedido.observaciones ?? "");

        dynamic _resCart = await ApiRequest.execAPI(
            _reqCart.uriReq, _reqCart.bodyReq,
            update: true);

        if (_resCart != null && _resCart['ID'] != null) {
          syncedPedidos++;
          pedidosController.removePedidoOffline(pedido);
          Fluttertoast.showToast(
            msg: 'Pedido $syncedPedidos de $totalPedidos sincronizado',
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.SNACKBAR,
          );
          i--;
        }
      }

      EasyLoading.showSuccess(
          'Todos los pedidos offline han sido sincronizados');
    }

    EasyLoading.show(status: 'Sincronizando...');

    await PrefsManager.clearPrefs();

    Get.find<SyncController>().reset();

    /* Articulos */
    List<dynamic> arts = [];
    var artsFast = [];

    DARequestModel _req = ApiRequest.getArticulosFast();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    if (data != null) {
      artsFast = transformDataOptimizedV3(data);
    }

    for (int i = 0; i < artsFast.length; i++) {
      dynamic art = artsFast[i];
      if (art != null) {
        if (art["PrecioLista"] == null || art["PrecioLista"] == 0) {
          if (art['ListaPrecios'] != null) {
            // Validamos si es un mapa o una lista
            if (art['ListaPrecios'] is Map) {
              dynamic precio = art['ListaPrecios']['Precio'];
              if (precio == null || precio == 0) {
              } else {
                // Agregamos existencias
                dynamic disponibleMap = art['Disponible'];
                if (disponibleMap != null) {
                  if (disponibleMap is Map) {
                    art['Existencia'] = disponibleMap['Disponible'].toString();
                  } else if (disponibleMap is List) {
                    // Si es una lista, buscamos que haya un match con dispAlmacen
                    for (int i = 0; i < disponibleMap.length; i++) {
                      if (disponibleMap[i]['Almacen'] ==
                          Get.find<UserConfigController>()
                              .usuarioMovil
                              .dispAlmacen) {
                        art['Existencia'] =
                            disponibleMap[i]['Disponible'].toString();
                        break;
                      }
                    }
                  }
                }
                art['checked'] = false;
                // Agregamos el articulo
                arts.add(art);
              }
            } else if (art["ListaPrecios"][0]['Precio'] == null ||
                art["ListaPrecios"][0]['Precio'] == 0) {
            } else {
              // Agregamos existencias
              dynamic disponibleMap = art['Disponible'];
              if (disponibleMap != null) {
                if (disponibleMap is Map) {
                  art['Existencia'] = disponibleMap['Disponible'].toString();
                } else if (disponibleMap is List) {
                  // Si es una lista, buscamos que haya un match con dispAlmacen
                  for (int i = 0; i < disponibleMap.length; i++) {
                    if (disponibleMap[i]['Almacen'] ==
                        Get.find<UserConfigController>()
                            .usuarioMovil
                            .dispAlmacen) {
                      art['Existencia'] =
                          disponibleMap[i]['Disponible'].toString();
                      break;
                    }
                  }
                }
              }
              art['checked'] = false;
              // Agregamos el articulo
              arts.add(art);
            }
          }
        } else {
          // Agregamos existencias
          dynamic disponibleMap = art['Disponible'];
          if (disponibleMap != null) {
            if (disponibleMap is Map) {
              art['Existencia'] = disponibleMap['Disponible'].toString();
            } else if (disponibleMap is List) {
              // Si es una lista, buscamos que haya un match con dispAlmacen
              for (int i = 0; i < disponibleMap.length; i++) {
                if (disponibleMap[i]['Almacen'] ==
                    Get.find<UserConfigController>().usuarioMovil.dispAlmacen) {
                  art['Existencia'] = disponibleMap[i]['Disponible'].toString();
                  break;
                }
              }
            }
          }
          art['checked'] = false;
          // Agregamos el articulo
          arts.add(art);
        }
      }
    }

    // Validamos que si la existencia es null, la agregamos como -
    arts.forEach((element) {
      if (element['Existencia'] == null || element['Existencia'] == 'null') {
        element['Existencia'] = '-';
      }
    });

    PrefsManager.setArticulos(articulos: json.encode(arts));

    /* Juegos */
    DARequestModel _reqJuego = ApiRequest.getJuegos();
    dynamic dataJuego =
        await ApiRequest.execAPI(_reqJuego.uriReq, _reqJuego.bodyReq);
    PrefsManager.setJuegos(juegos: json.encode(dataJuego));

    JuegoController juegoController = Get.find<JuegoController>();
    juegoController.reset(isSync: true);
    dataJuego.forEach((element) {
      juegoController.setArtJuego(element);
    });
    juegoController.setArtJuegoCopy();

    /* Detalle de Juegos */
    DARequestModel _reqJuegoD = ApiRequest.getJuegosD();
    dynamic dataJuegoD =
        await ApiRequest.execAPI(_reqJuegoD.uriReq, _reqJuegoD.bodyReq);
    PrefsManager.setJuegosD(juegosD: json.encode(dataJuegoD));

    dataJuegoD.forEach((element) {
      juegoController.setArtJuegoD(element);
    });
    juegoController.setArtJuegoDCopy();

    /* Opciones */
    List<dynamic> opcs = [];

    DARequestModel _reqOpciones = ApiRequest.getOpciones();

    dynamic dataOpciones =
        await ApiRequest.execAPI(_reqOpciones.uriReq, _reqOpciones.bodyReq);

    for (int i = 0; i < dataOpciones.length; i++) {
      dynamic opc = dataOpciones[i];
      opcs.add(opc);
    }

    PrefsManager.setOpciones(opciones: json.encode(opcs));

    OpcionesController _opcionesController = Get.find<OpcionesController>();
    _opcionesController.reset();
    _opcionesController.init();

    /* Juegos */
    DARequestModel _reqJuegos = ApiRequest.getJuegos();
    dynamic dataJuegos =
        await ApiRequest.execAPI(_reqJuegos.uriReq, _reqJuegos.bodyReq);

    PrefsManager.setJuegos(juegos: json.encode(dataJuegos));

    /* Juegos Detalle */
    DARequestModel _reqJuegosD = ApiRequest.getJuegosD();
    dynamic dataJuegosD =
        await ApiRequest.execAPI(_reqJuegosD.uriReq, _reqJuegosD.bodyReq);

    PrefsManager.setJuegosD(juegosD: json.encode(dataJuegosD));

    /* Clientes */
    DARequestModel _reqCliente = ApiRequest.getClientes();
    dynamic dataCliente =
        await ApiRequest.execAPI(_reqCliente.uriReq, _reqCliente.bodyReq);

    PrefsManager.setClientes(clientes: json.encode(dataCliente));

    /* Catalogos */
    DARequestModel _reqCatalog = ApiRequest.getCatalogos();
    dynamic dataCatalog =
        await ApiRequest.execAPI(_reqCatalog.uriReq, _reqCatalog.bodyReq);

    PrefsManager.setCatalogos(catalogos: json.encode(dataCatalog));

    /* Citas */
    DARequestModel _reqCitas = ApiRequest.getCitas();
    dynamic dataCitas =
        await ApiRequest.execAPI(_reqCitas.uriReq, _reqCitas.bodyReq);

    PrefsManager.setCitas(citas: json.encode(dataCitas));

    /* Cambios Clientes */
    List<String>? _cambiosClientes = await PrefsManager.getCambiosClientes();
    if (_cambiosClientes != null && _cambiosClientes.length > 0) {
      for (int i = 0; i < _cambiosClientes.length; i++) {
        dynamic _cambioCliente = json.decode(_cambiosClientes[i]);
        DARequestModel _reqCambioCliente = ApiRequest.createCliente(
          cliente: _cambioCliente['cliente'].toString(),
          nombre: _cambioCliente['nombre'].toString(),
          razonSocial: _cambioCliente['razonSocial'].toString(),
          rfc: _cambioCliente['rfc'].toString(),
          contacto: _cambioCliente['contacto'].toString(),
          telefono: _cambioCliente['telefono'].toString(),
          email: _cambioCliente['email'].toString(),
          direccion: _cambioCliente['direccion'].toString(),
          dirNumero: _cambioCliente['dirNumero'].toString(),
          numInterior: _cambioCliente['numInterior'].toString(),
          entreCalles: _cambioCliente['entreCalles'].toString(),
          delegacion: _cambioCliente['delegacion'].toString(),
          colonia: _cambioCliente['colonia'].toString(),
          poblacion: _cambioCliente['poblacion'].toString(),
        );
        await ApiRequest.execAPI(
            _reqCambioCliente.uriReq, _reqCambioCliente.bodyReq,
            update: true);

        PrefsManager.setCambiosClientes(cambio: '', removeFirst: true);
      }
    }

    /* Historial de cobros */
    DARequestModel _reqHistCobros = ApiRequest.historialCobro();
    dynamic dataHistCobro =
        await ApiRequest.execAPI(_reqHistCobros.uriReq, _reqHistCobros.bodyReq);

    PrefsManager.setServerHistorialCobros(historial: dataHistCobro['Cobros']);

    /* Historial de pedidos */
    DARequestModel _reqHistPedidos = ApiRequest.historial();
    dynamic dataHist = await ApiRequest.execAPI(
        _reqHistPedidos.uriReq, _reqHistPedidos.bodyReq);

    PrefsManager.setServerHistorialPedidos(historial: dataHist['Pedidos']);

    /* Precios Con Impuesto */
    TicketController _ticketController = Get.find<TicketController>();
    DARequestModel _reqPrecioImpuesto = ApiRequest.getConfiguracionPrecios();
    dynamic _resPrecioImpuesto = await ApiRequest.execAPI(
        _reqPrecioImpuesto.uriReq, _reqPrecioImpuesto.bodyReq);

    PrefsManager.setPrecioConImpuesto(
        _resPrecioImpuesto[0]['VentaPreciosImpuestoIncluido']);
    _ticketController.setPrecioConImpuesto(
        _resPrecioImpuesto[0]['VentaPreciosImpuestoIncluido']);

    Get.find<SyncController>().setSynced = true;

    /* Ultima Sincronizacion */
    PrefsManager.setUltimaSincronizacion();
    EasyLoading.dismiss();
    await Future.delayed(Duration(milliseconds: 100));
    navService.pushNamedAndRemoveUntil('home');
  }

  static List<Widget> appMenu(BuildContext context) {
    try {
      List<Widget> _navBarOpt = [];

      _navBarOpt.add(DAInput(
        refID: 'ultimaSincronizacion',
        controller: new TextEditingController(text: DateTime.now().toString()),
        label: 'Ultima Sincronización',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));
      _navBarOpt.add(DAInput(
        refID: 'usuario',
        controller: new TextEditingController(text: prov.session.usuario),
        label: 'Usuario',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));
      _navBarOpt.add(DAInput(
        refID: 'agenteDeVentas',
        controller: new TextEditingController(text: prov.session.agente),
        label: 'Agente de Ventas',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));
      _navBarOpt.add(DAInput(
        refID: 'email',
        controller: new TextEditingController(text: prov.session.username),
        label: 'Correo Electrónico',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));
      _navBarOpt.add(Container(
        padding: EdgeInsets.symmetric(horizontal: 16.0),
        child: SwitchListTile(
          value: true,
          onChanged: null,
          title: Text('Almacén'),
        ),
      ));
      _navBarOpt.add(DADivider());
      _navBarOpt.add(Container(
        padding: EdgeInsets.symmetric(horizontal: 16.0),
        child: SwitchListTile(
          value: false,
          onChanged: null,
          title: Text('Descuento %'),
        ),
      ));

      SyncChangesController _syncChangesController =
          Get.find<SyncChangesController>();

      if (_syncChangesController.hasChanges) {
        _navBarOpt.add(
          Badge(
            child: ElevatedButton.icon(
                onPressed: sync,
                icon: Icon(Icons.sync),
                label: Text('Sincronizar')),
          ),
        );
      } else {
        _navBarOpt.add(
          ElevatedButton.icon(
              onPressed: sync,
              icon: Icon(Icons.sync),
              label: Text('Sincronizar')),
        );
      }

      Widget _menu = ListTile(
        title: Text('Inicio'),
        leading: Container(
          padding: EdgeInsets.only(right: 5),
          child: Icon(
            Icons.home_outlined,
            color: Colors.black,
          ),
        ),
        onTap: () async {
          navService.pushNamedAndRemoveUntil('home');
        },
      );

      _navBarOpt.add(_menu);

      _navBarOpt.add(ListTile(
        title: Text(
          'Cerrar Sesión',
        ),
        leading: Container(
          padding: EdgeInsets.only(right: 5),
          child: Icon(
            Icons.logout,
            color: Colors.black,
          ),
        ),
        onTap: () async {
          await AppConfig.logOut();
        },
      ));

      if (kDebugMode) {
        _navBarOpt.add(ListTile(
          title: Text(
            'Vaciar',
          ),
          leading: Container(
            padding: EdgeInsets.only(right: 5),
            child: Icon(
              Icons.delete,
              color: Colors.black,
            ),
          ),
          onTap: () async {
            await PrefsManager.clearPrefs(
                clearCart: true, clearCobro: true, clearHistorial: true);
            Fluttertoast.showToast(
                msg: 'Datos eliminados',
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                timeInSecForIosWeb: 1,
                backgroundColor: Colors.black,
                textColor: Colors.white,
                fontSize: 16.0);
          },
        ));
      }

      _navBarOpt.add(VersionDisplay());

      return _navBarOpt;
    } catch (e) {
      DAToast(context, e.toString());
      return [];
    }
  }

  static Future<void> logOut() async {
    final httpProv = new HttpProvider();
    bool resetEstacion = await httpProv.logOut();
    if (resetEstacion) {
      prov.resetEstacion();
    }
    prov.reset();
    PrefsManager.setSessionHasClosed(true);
    Get.find<SyncController>().setSynced = false;
    navService.pushNamedAndRemoveUntil('login');
  }
}
