import 'package:get/get.dart';
import 'package:pedidosdipath/src/controllers/user_config_controller.dart';

import '../DAPackagesRef.dart';
import 'app_config.dart';

class ApiRequest {
  static ApiRequest _instancia = new ApiRequest._internal();

  ApiRequest._internal();
  factory ApiRequest() {
    return _instancia;
  }

  // Valida sesión
  static Future<dynamic> execAPI(String uri, dynamic body,
      {bool? update}) async {
    try {
      if ((update ?? false) == true) {
        return DAMainLoadingProvider.updateAPI(uri, body);
      }

      return DAMainLoadingProvider.execAPI(uri, body);
    } catch (e) {
      if (e.toString() == "SESIÓN EXPIRADA") {
        AppConfig.logOut();
        return;
      }
      throw e.toString();
    }
  }

  // Configuración de Usuario
  static DARequestModel userConfig() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spGetAppUsuario_PMovil",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        //"Sucursal": "{{Sucursal}}",
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Precios Con Impuesto
  static DARequestModel getConfiguracionPrecios() {
    String uri =
        "/CRUD/EmpresaCfg?c=VentaPreciosImpuestoIncluido&q=Empresa:{{Empresa}}";
    DARequestModel _req = new DARequestModel(uriReq: uri);

    _req.uriReq = DAController.sessionUrl(uri);
    return _req;
  }

  // Tipo Impuesto
  static DARequestModel getTipoImpuesto() {
    String uri = "/CRUD/EmpresaGral?c=TipoImpuesto&q=Empresa:{{Empresa}}";
    DARequestModel _req = new DARequestModel(uriReq: uri);

    _req.uriReq = DAController.sessionUrl(uri);
    return _req;
  }

  // Citas
  static DARequestModel getCitas() {
    DARequestModel _req = new DARequestModel(uriReq: "/PMovil/Citas");
    return _req;
  }

  static DARequestModel cancelCita(
      {required String id,
      required double lat,
      required double lng,
      required String motivo}) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Citas/",
      bodyReq: {
        "IDCita": id,
        "MapaLatitud": lat,
        "MapaLongitud": lng,
        "AccionMovil": 'Cancelado',
        "SubSituacion": motivo,
      },
    );
    return _req;
  }

  static DARequestModel rescheduleCita(
      {required String id, required double lat, required double lng}) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Citas/",
      bodyReq: {
        "IDCita": id,
        "MapaLatitud": lat,
        "MapaLongitud": lng,
        "AccionMovil": 'Re Agendado',
      },
    );
    return _req;
  }

  // Articulos
  static DARequestModel getArticulos() {
    DARequestModel _req = new DARequestModel(uriReq: "/PMovil/JSONArt");
    return _req;
  }

  static DARequestModel getArticulosFast() {
    DARequestModel _req = new DARequestModel(uriReq: "/PMovil/Articulos");
    return _req;
  }

  // Opciones
  static DARequestModel getOpciones() {
    DARequestModel _req = new DARequestModel(uriReq: "/PROC/spPM_Opciones");
    _req.bodyReq = {};
    return _req;
  }

  // Juegos
  static DARequestModel getJuegos() {
    DARequestModel _req = new DARequestModel(uriReq: "/CRUD/ArtJuego");
    return _req;
  }

  // Juegos Detalle
  static DARequestModel getJuegosD() {
    DARequestModel _req = new DARequestModel(uriReq: "/CRUD/ArtJuegoD");
    return _req;
  }

  // Catalogos
  static DARequestModel getCatalogos() {
    DARequestModel _req = new DARequestModel(uriReq: "/PMovil/Catalogos");
    return _req;
  }

  // Clientes
  static DARequestModel getClientes() {
    DARequestModel _req = new DARequestModel(uriReq: "/PMovil/Cliente");
    return _req;
  }

  // Sucursales Del Cliente del Agente
  static DARequestModel getSucursalesAgentes({required String agente}) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPM_AgentesSucursalesCte",
      bodyReq: {
        "Agente": agente,
      },
    );
    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel createCliente({
    String cliente = '',
    required String nombre,
    required String razonSocial,
    required String rfc,
    required String contacto,
    required String telefono,
    required String email,
    required String direccion,
    required String dirNumero,
    required String numInterior,
    required String entreCalles,
    String delegacion = '',
    String colonia = '',
    String poblacion = '',
    String codigoPostal = '',
  }) {
    DARequestModel _req =
        new DARequestModel(uriReq: "/PMovil/Cliente", bodyReq: {
      "Cliente": cliente,
      "Nombre": nombre,
      "NombreCorto": nombre,
      "Direccion": direccion,
      "DireccionNumero": dirNumero,
      "DireccionNumeroInt": numInterior,
      "EntreCalles": entreCalles,
      "Delegacion": delegacion,
      "Colonia": colonia,
      "Poblacion": poblacion,
      "Estado": "",
      "Pais": "",
      "CodigoPostal": codigoPostal,
      "RFC": rfc,
      "Telefonos": telefono,
      "TelefonosLada": "",
      "ListaPrecios": null,
      "DefMoneda": "",
      "MapaLatitud": null,
      "MapaLongitud": null,
      "MapaPrecision": null,
      "AlmacenDef": "",
      "Contacto1": contacto,
      "Contacto2": "",
      "eMail1": email,
      "eMail2": "",
      "CreditoLimite": null,
      "Descuento": null,
      "EnviarA": null
    });
    return _req;
  }

  // Ofertas
  static DARequestModel preparaOfertas(
      {required String cliente,
      required String listaPrecios,
      required String almacen,
      required List<dynamic> carrito}) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Oferta",
      bodyReq: {
        "ID": null,
        "Proyecto": "",
        "Moneda": "Pesos",
        "TipoCambio": 1,
        "Almacen": almacen,
        "ListaPrecios": listaPrecios,
        "FechaEmision": null,
        "Cliente": cliente,
        "Referencia": "",
        "Observaciones": "PREPARA OFERTA",
        "DescuentoGlobal": null,
        "IDCita": null,
        "detalle": carrito
      },
    );
    return _req;
  }

  static DARequestModel getOfertas({
    required String id,
    required String listaPrecios,
    required String fechaEmision,
    required String cliente,
    String concepto = "",
    String almacen = "",
    required List detalle,
  }) {
    // ignore: unused_local_variable
    DASessionProvider prov = DASessionProvider();
    DARequestModel _req = new DARequestModel(uriReq: "/Ofertas", bodyReq: {
      "Usuario": "{{Usuario}}",
      "Empresa": "{{Empresa}}",
      "Sucursal": Get.find<UserConfigController>().usuarioMovil.sucursal,
      "ID": id,
      "Moneda": "Pesos",
      "TipoCambio": 1,
      "ListaPrecios": listaPrecios,
      "FechaEmision": fechaEmision,
      "Cliente": cliente,
      "Concepto": concepto,
      "Agente": Get.find<UserConfigController>().usuarioMovil.agente,
      "Referencia": "PEDIDO MOVIL $id",
      "Observaciones": "PEDIDO MOVIL (OFERTAS)",
      "Almacen": almacen,
      "DescuentoGlobal": '',
      "detalle": detalle,
    });
    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Ajuste Dipath spWebPrecioPoliticaDipath1Wrapper
  static DARequestModel ajusteDipath({
    required String cliente,
    required String articulo,
    required num cantidad,
    required num precioInicial,
    num? sucursalMov,
    num? sucursalAgente,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Cliente": cliente,
      "Articulo": articulo,
      "Cantidad": cantidad,
      "PrecioInicial": precioInicial,
      "SucursalMov": sucursalMov ?? "{{Sucursal}}"
    };

    if (sucursalAgente != null) {
      _preBodyReq["SucursalAgente"] = sucursalAgente;
    }

    DARequestModel _req =
        DARequestModel(uriReq: "/PROC/spWebPrecioPoliticaDipath1Wrapper");

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);

    var sucursalMovFinal = sucursalMov;
    if (sucursalMov == null ||
        (sucursalMov is String && sucursalMov.toString().trim() == "")) {
      if (sucursalAgente != null) {
        sucursalMovFinal = sucursalAgente;
      }
    }

    _req.bodyReq?["SucursalMov"] = sucursalMovFinal;

    return _req;
  }

  // Obtención Zona Fronteriza
  static DARequestModel get obtenerZonaFronteriza {
    String uriRoute = "/CRUD/Sucursal?q=Sucursal:{{Sucursal}}&c=ZonaImpuesto";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    return _req;
  }

  // Obtener Sucursales del Agente
  static DARequestModel obtenerSucursalesAgente() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPM_ObtenerSucursalesAgente",
      bodyReq: {
        "Usuario": "{{Usuario}}",
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Pedido
  static DARequestModel pedido(
      {required String cliente,
      required String? idCita,
      required String? condicionPago,
      required String? listaPrecios,
      required double lat,
      required double lng,
      required String? observaciones,
      required List<dynamic> carrito}) {
    if (idCita == '' || idCita == 'null') {
      idCita = null;
    }
    if (condicionPago == '') {
      condicionPago = null;
    }
    if (listaPrecios == '') {
      listaPrecios = null;
    }
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Pedido",
      bodyReq: {
        "ID": null,
        "Proyecto": "",
        "Moneda": "Pesos",
        "TipoCambio": 1,
        "ListaPrecios": listaPrecios,
        "FechaEmision": null,
        "Cliente": cliente,
        "Referencia": "",
        "Observaciones": (observaciones == '') ? "PEDIDO MOVIL" : observaciones,
        "DescuentoGlobal": null,
        "IDCita": idCita,
        "CondicionPago": condicionPago,
        "MapaLatitud": lat,
        "MapaLongitud": lng,
        "detalle": carrito
      },
    );
    return _req;
  }

  // CxcInfo
  static DARequestModel cxcInfo({required String cliente}) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/CxcInfo?Cliente=$cliente",
    );
    return _req;
  }

  // Historial
  static DARequestModel historial() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Historial",
    );
    return _req;
  }

  static DARequestModel historialCobro() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Historial?Tipo=Cobro",
    );
    return _req;
  }

  // Cobro
  static DARequestModel cobro({
    required String moneda,
    required String cliente,
    required String observaciones,
    required double importe,
    required double latitud,
    required double longitud,
    required bool aplicaManual,
    required String formaCobro,
    required dynamic detalle,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PMovil/Cobro",
      bodyReq: {
        "ID": null,
        "Moneda": moneda,
        "TipoCambio": 1,
        "FechaEmision": null,
        "Cliente": cliente,
        "Observaciones": observaciones,
        "Importe": importe,
        "MapaLatitud": latitud,
        "MapaLongitud": longitud,
        "FormaCobro": formaCobro,
        "AplicaManual": aplicaManual,
        "detalle": detalle,
      },
    );
    return _req;
  }

  // Rutas
  static DARequestModel get rutasList {
    String uriRoute = "/PROC/spQuiquin_Rutas";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    Map<String, dynamic> _preBodyReq = {
      "Detalle": false,
      "Agente": "{{Agente}}",
    };

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);
    return _req;
  }

  static DARequestModel rutasClientesList({required String idRuta}) {
    String uriRoute = "/PROC/spQuiquin_Rutas";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    Map<String, dynamic> _preBodyReq = {
      "Detalle": true,
      "Agente": "{{Agente}}",
      "ID": idRuta
    };

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);
    return _req;
  }

  // Clientes
  static DARequestModel clientesData(String clave) {
    String uriRoute = "/PROC/spQuiquin_DatosCliente";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    Map<String, dynamic> _preBodyReq = {"Cliente": clave};

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);
    return _req;
  }

  static DARequestModel get obtenerSubSituaciones {
    String uriRoute =
        "/CRUD/CampanaTipoSubSituacion?q=Situacion:Cancelado&lk=CampanaTipo:Quiquin";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    return _req;
  }

  // Obtener Tipo De Cambio
  static DARequestModel get obtenerTipoCambio {
    DARequestModel _req = new DARequestModel(
      uriReq:
          "/CRUD/Mon?q=Moneda:${Get.find<UserConfigController>().monedaUsuario}&c=TipoCambio",
    );

    return _req;
  }

  // Datos Regenerar Ticket
  static DARequestModel datosRegenerarTicket({required int id}) {
    Map<String, dynamic> _preBodyReq = {"ID": id};
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPM_DatosRegenerarTicket",
      bodyReq: _preBodyReq,
    );
    return _req;
  }

  static DARequestModel cancelarVisita({
    required bool isCheckIn,
    required String id,
    required String rid,
    required String situacion,
    required String subSituacion,
    required String latitud,
    required String longitud,
  }) {
    String uriRoute = "/PROC/spQuiquin_CheckIn_Cancelacion";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    Map<String, dynamic> _preBodyReq = {
      "Usuario": "{{Usuario}}",
      "Sucursal": "{{Sucursal}}",
      "CheckIn": isCheckIn,
      "ID": id,
      "RID": rid,
      "Situacion": situacion,
      "SubSituacion": subSituacion,
      "MapaLatitud": latitud,
      "MapaLongitud": longitud
    };

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);
    return _req;
  }

  static DARequestModel registrarUbicacion({
    required bool isCheckIn,
    required String id,
    required String rid,
    required String situacion,
    required String subSituacion,
    required String latitud,
    required String longitud,
  }) {
    String uriRoute = "/PROC/spQuiquin_CheckIn_Cancelacion";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    Map<String, dynamic> _preBodyReq = {
      "Usuario": "{{Usuario}}",
      "Sucursal": "{{Sucursal}}",
      "CheckIn": isCheckIn,
      "ID": id,
      "RID": rid,
      "Situacion": situacion,
      "SubSituacion": subSituacion,
      "MapaLatitud": latitud,
      "MapaLongitud": longitud
    };

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);
    return _req;
  }

  // Cobranza
  static DARequestModel facturasAsignadasList(
      {String campanaID = '', String cliente = ''}) {
    String uriRoute = '/Quiquin/FacturasAsignadas?CampanaID=' +
        campanaID +
        '&Cliente=' +
        cliente;

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
      bodyReq: null,
    );

    return _req;
  }

  static DARequestModel generarCobro({
    required String moneda,
    required String cliente,
    required String formaPago,
    required String aplica,
    required String aplicaID,
    required String importe,
    required String latitud,
    required String longitud,
  }) {
    String uriRoute = "/Quiquin/Cobro";

    Map<String, dynamic> _preBodyReq = {
      "Moneda": moneda,
      "TipoCambio": 1,
      "Cliente": cliente,
      "Observaciones": "COBRO QUIQUIN",
      "Importe": importe,
      "MapaLatitud": latitud,
      "MapaLongitud": longitud,
      "Movimiento": "Cobro",
      "AplicaManual": 1,
      "FormaPago": formaPago,
      "detalle": [
        {"Aplica": aplica, "AplicaID": aplicaID, "Importe": importe}
      ]
    };

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
      bodyReq: _preBodyReq,
    );

    return _req;
  }

  // Pedidos
  static DARequestModel pedidosList({required String cliente}) {
    String uriRoute = '/Quiquin/Historial?Tipo=Pedido&Cliente=' + cliente;

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
      bodyReq: null,
    );

    return _req;
  }

  static DARequestModel pedidoDet({required String id}) {
    String uriRoute = '/PROC/spQuiquin_DetallePedidosCotizacion';

    DARequestModel _req = new DARequestModel(
        uriReq: DAController.sessionUrl(uriRoute),
        bodyReq: {"ID": id, "Pedido": true, "Cliente": ""});

    return _req;
  }

  static DARequestModel pedidoGuardar({
    required String pedidoID,
    required String renglon,
    required String cantidad,
    required String descuentoLinea,
    String? articulo,
    String? precio,
    String? unidad,
  }) {
    Map<String, dynamic> _preBodyReq = {
      'Usuario': '{{Usuario}}',
      'Estacion': '{{Estacion}}',
      'ID': pedidoID,
      'Renglon': renglon,
      'Cantidad': cantidad,
      'DescuentoLinea': descuentoLinea,
    };

    if (articulo != null) {
      _preBodyReq['Articulo'] = articulo;
    }

    if (precio != null) {
      _preBodyReq['Precio'] = precio;
    }

    if (unidad != null) {
      _preBodyReq['Unidad'] = unidad;
    }

    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spQuiquin_Pedido_Detalle',
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel agregarArticulo({
    required String pedidoID,
    required String cantidad,
    required String articulo,
    required String precio,
    required String unidad,
    required String descuentoLinea,
  }) {
    Map<String, dynamic> _preBodyReq = {
      'Usuario': '{{Usuario}}',
      'Estacion': '{{Estacion}}',
      'ID': pedidoID,
      'Cantidad': cantidad,
      'Articulo': articulo,
      'Precio': precio,
      'Unidad': unidad,
      'DescuentoLinea': descuentoLinea,
    };

    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spQuiquin_Pedido_Detalle',
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel catsReacomodoArt({
    String? articulo,
    String? almacen,
    String? tipoPos,
    String? tarima,
    String? tipoCat,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Empresa": "{{Empresa}}",
      "Sucursal": "{{Sucursal}}",
    };

    if (articulo != null) {
      _preBodyReq['Articulo'] = articulo;
    }

    if (almacen != null) {
      _preBodyReq['Almacen'] = almacen;
    }

    if (tipoPos != null) {
      _preBodyReq['Tipo'] = tipoPos;
    }

    if (tarima != null) {
      _preBodyReq['Tarima'] = tarima;
    }

    if (tipoCat != null) {
      _preBodyReq['TipoCat'] = tipoCat;
    }

    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spQuiquin_PosDtoTarArtLista",
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel jsonArt({
    String? articulo = '',
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/Quiquin/JSONArt?Articulo=$articulo",
      bodyReq: null,
    );

    return _req;
  }

  static DARequestModel nuevoPedido({
    required String cliente,
    required String movimiento,
    required String moneda,
    // required String almacen,
  }) {
    Map<String, dynamic> _preBodyReq = {
      'Usuario': '{{Usuario}}',
      'Empresa': '{{Empresa}}',
      'Sucursal': '{{Sucursal}}',
      'Agente': '{{Agente}}',
      // 'Almacen': almacen,
      'Cliente': cliente,
      'Moneda': moneda,
      'Movimiento': movimiento,
    };

    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spQuiquin_Pedido',
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel confirmarPedido({required String pedidoID}) {
    DARequestModel _req = new DARequestModel(
      uriReq: '/CRUD/Venta?q=ID:$pedidoID',
      bodyReq: {'Estatus': 'CONFIRMAR'},
    );

    return _req;
  }

  // Catalogos
  static DARequestModel obtenerCatalogos({
    String? tipo = '',
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/Quiquin/Catalogos?Tipo=$tipo",
      bodyReq: null,
    );

    return _req;
  }

  static DARequestModel catAlmacen() {
    // Only for PROC
    // Map<String, String> body = {
    //   'Personal': '{{Usuario}}',
    //   'MapaLatitud': latitud.toString(),
    //   'MapaLongitud': longitud.toString(),
    //   'Archivo': foto,
    // };

    DARequestModel _req = new DARequestModel(
      uriReq: "/CRUD/Alm",
      bodyReq: null,
    );

    //_req.bodyReq = DAController.sessionRequest(_req.bodyReq); // Para reemplazar variables de sesión
    return _req;
  }

  // Generales
  static DARequestModel infoSerieLote({
    required String id,
    required String modulo,
    required String art,
    required String renglon,
    String? serieLote,
    String? serieLotePrev,
    String? cantidad,
    bool? info,
  }) {
    Map<String, dynamic> _bodyReq = {
      "Empresa": "{{Empresa}}",
      "Sucursal": "{{Sucursal}}",
      "ModuloID": id,
      "Modulo": modulo,
      "Articulo": art,
      "RenglonID": renglon,
    };

    if (serieLote != null && serieLote != '') {
      _bodyReq["SerieLote"] = serieLote;
    }

    if (serieLotePrev != null && serieLotePrev != '') {
      _bodyReq["SerieLotePrev"] = serieLotePrev;
    }

    if (cantidad != null && cantidad != '') {
      _bodyReq["Cantidad"] = cantidad;
    }

    if (info != null) {
      _bodyReq["Info"] = info;
    }

    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_InfoSerieLote",
      bodyReq: _bodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel movListadoMapeo(String tipo) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_MapeoMovMovilListado",
      bodyReq: {"Clave": tipo},
    );

    return _req;
  }

  static DARequestModel catMontacargas() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/CRUD/Montacarga",
    );

    return _req;
  }

  // Recepcion
  static DARequestModel recnCats() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/CRUD/MapeoMovMovil?q=Modulo:COMS&c=Mov|MovFinal",
    );

    return _req;
  }

  static DARequestModel listaCOMSoPendientes(mov) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/CRUD/vwAppWMS_MovsPendientes?q=Empresa:{{Empresa}}|Mov:" +
          (mov ?? ''),
    );

    _req.uriReq = DAController.sessionUrl(_req.uriReq);
    return _req;
  }

  static DARequestModel infoDetalleMovCOMSO(String id) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_InfoDetalleMovCOMS_O",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "ID": id,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel cAfectarRecepcionD(
      String id, String renglon, String cantidadafectar) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/CRUD/CompraD?q=ID:" + id + '|RenglonID:' + renglon,
      bodyReq: {
        "CantidadA": cantidadafectar,
      },
    );

    return _req;
  }

  static DARequestModel afectarRecepcion({
    required String mov,
    required String movid,
    required String tipoEntradaGenerar,
    required String anden,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TMAAfectarCOMS_O",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Estacion": "{{Estacion}}",
        "Mov": mov,
        "MovID": movid,
        "TipoEntradaGenerar": tipoEntradaGenerar,
        "AndenRecibo": anden,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Acomodo
  static DARequestModel tarimaXAfectar({
    required String mov,
    required String montacarga,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TarimasPorAfectarUnion",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Agente": "{{Agente}}",
        "Sucursal": "{{Sucursal}}",
        "Mov": mov,
        "Montacarga": montacarga,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel tarimaAfectarMovSADO({
    required String mov,
    required String movID,
    required String idTma,
    required String renglon,
    required String tarima,
    required String posicion,
    required String posicionDestino,
    required String montacargas,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TMAAfectarTMA_SADO",
      bodyReq: {
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Usuario": "{{Usuario}}",
        "Agente": "{{Agente}}",
        "Estacion": "{{Estacion}}",
        "Mov": mov,
        "MovID": movID,
        "IdTma": idTma,
        "Renglon": renglon,
        "Tarima": tarima,
        "Posicion": posicion,
        "PosicionDestino": posicionDestino,
        "Montacargas": montacargas,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel tarimaAfectarMovOADO({
    required String mov,
    required String movID,
    required String idTma,
    required String tarima,
    required String posicionDestino,
    required String montacargas,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TMAAfectarTMA_OADO",
      bodyReq: {
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Usuario": "{{Usuario}}",
        "Agente": "{{Agente}}",
        "Estacion": "{{Estacion}}",
        "Mov": mov,
        "MovID": movID,
        "IdTma": idTma,
        "Tarima": tarima,
        "PosicionDestino": posicionDestino,
        "Montacargas": montacargas,
        "CantidadParcial": "0"
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Surtido
  static DARequestModel pckListado({
    required String mov,
    required String montacarga,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TarimaPckListado",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Agente": "{{Agente}}",
        "Sucursal": "{{Sucursal}}",
        "Mov": mov,
        "Montacarga": montacarga,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel pckListadoDetail({
    required String almacen,
    required String mov,
    required String movID,
    required String tarima,
    required String posicion,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_ArticuloEspPosicion",
      bodyReq: {
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Almacen": almacen,
        "Mov": mov,
        "MovID": movID,
        "Tarima": tarima,
        "Posicion": posicion,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel pckTarimasPorCerrarLista() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TarimasPorCerrarLista",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Agente": "{{Agente}}",
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel pckProcesarTarSurTraPCK({
    required String moduloID,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_CerrarTarSurTraPCK",
      bodyReq: {
        "Empresa": "{{Empresa}}",
        "Usuario": "{{Usuario}}",
        "Estacion": "{{Estacion}}",
        "ModuloID": moduloID,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel afectarSurtidoOSURP({
    required String idTma,
    required String mov,
    required String movID,
    required String montacargas,
    required String tarima,
    required String posicion,
    required String articulo,
    required String cantidad,
    required String unidad,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_TMAAfectarTMA_OSURP",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Agente": "{{Agente}}",
        "Estacion": "{{Estacion}}",
        "IdTma": idTma,
        "Mov": mov,
        "MovID": movID,
        "Montacargas": montacargas,
        "Tarima": tarima,
        "Posicion": posicion,
        "Articulo": articulo,
        "Cantidad": cantidad,
        "Unidad": unidad
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Conteo
  static DARequestModel invListConteo() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_ConteoInventarioLista",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Agente": "{{Agente}}",
        "Clave": "INV.IF",
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel reqCatsNewInv() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_ConteoNuevoCatalogo",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Sucursal": "{{Sucursal}}",
        "Modulo": "INV",
        "Clave": "INV.IF",
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel reqGenerarInventario({
    required String mov,
    required String alm,
    required String mon,
    required String observaciones,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spAppWMS_GenerarInventarioFisico',
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Agente": "{{Agente}}",
        "Almacen": alm,
        "Moneda": mon,
        "Mov": mov,
        "Observaciones": observaciones
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel invFisicoDetReq({
    required String inventarioID,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spAppWMS_InventarioDetalle',
      bodyReq: {
        "Empresa": "{{Empresa}}",
        "InventarioID": inventarioID,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel invFisicoGuardar({
    required String inventarioID,
    required String renglon,
    required String cantidad,
    required String posicion,
    String? factor,
    String? articulo,
    String? almacen,
    String? unidad,
    String? codigo,
    String? tarima,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Usuario": "{{Usuario}}",
      "Empresa": "{{Empresa}}",
      "Sucursal": "{{Sucursal}}",
      "InventarioID": inventarioID,
      "Renglon": renglon,
      "Cantidad": cantidad,
      "Posicion": posicion,
    };

    if (factor != null) {
      _preBodyReq['Factor'] = factor;
    }

    if (articulo != null) {
      _preBodyReq['Articulo'] = articulo;
    }

    if (almacen != null) {
      _preBodyReq['Almacen'] = almacen;
    }

    if (unidad != null) {
      _preBodyReq['Unidad'] = unidad;
    }

    if (codigo != null) {
      _preBodyReq['Codigo'] = codigo;
    }

    if (tarima != null) {
      _preBodyReq['Tarima'] = tarima;
    }

    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spAppWMS_GuardarInventario',
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel catNewHallazgo() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_GuardarInventario_CatForm",
      bodyReq: {},
    );

    return _req;
  }

  static DARequestModel reqNewHallazgo({
    required String id,
    required String articulo,
    required String almacen,
    required String posicion,
    required String tarima,
    required String unidad,
    required String cantidad,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_GuardarInventario",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "InventarioID": id,
        "Cantidad": cantidad,
        "Posicion": posicion,
        "Articulo": articulo,
        "Almacen": almacen,
        "Unidad": unidad,
        "Tarima": tarima,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Reacomodo
  static DARequestModel catsReacomodo({
    String? articulo,
    String? almacen,
    String? tipoPos,
    String? tarima,
    String? tipoCat,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Empresa": "{{Empresa}}",
      "Sucursal": "{{Sucursal}}",
    };

    if (articulo != null) {
      _preBodyReq['Articulo'] = articulo;
    }

    if (almacen != null) {
      _preBodyReq['Almacen'] = almacen;
    }

    if (tipoPos != null) {
      _preBodyReq['Tipo'] = tipoPos;
    }

    if (tarima != null) {
      _preBodyReq['Tarima'] = tarima;
    }

    if (tipoCat != null) {
      _preBodyReq['TipoCat'] = tipoCat;
    }

    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_PosDtoTarArtLista",
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel genSolReacomodo({
    required String mov,
    required String montacarga,
    required String articulo,
    required String almacen,
    required String posicion,
    required String destino,
    required String tarima,
    required String unidad,
    required String cantidad,
    required String zona,
    required String prioridad,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spAppWMS_GenerarSolicitudReacomodo",
      bodyReq: {
        "Usuario": "{{Usuario}}",
        "Empresa": "{{Empresa}}",
        "Sucursal": "{{Sucursal}}",
        "Agente": "{{Agente}}",
        "Estacion": "{{Estacion}}",
        "Mov": mov,
        "MontaCarga": montacarga,
        "Articulo": articulo,
        "Almacen": almacen,
        "Posicion": posicion,
        "PosicionDestino": destino,
        "Tarima": tarima,
        "Unidad": unidad,
        "CantidadPicking": cantidad,
        "Zona": zona,
        "Prioridad": prioridad,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Buscar Información
  static DARequestModel getGenWMSInfo({
    String? articulo,
    String? tarima,
    String? posicion,
    String? codigo,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Empresa": "{{Empresa}}",
      "Sucursal": "{{Sucursal}}",
    };

    if (articulo != null) {
      _preBodyReq['Articulo'] = articulo;
    }

    if (tarima != null) {
      _preBodyReq['Tarima'] = tarima;
    }

    if (posicion != null) {
      _preBodyReq['Posicion'] = posicion;
    }

    if (codigo != null) {
      _preBodyReq['Codigo'] = codigo;
    }

    DARequestModel _req = new DARequestModel(
      uriReq: '/PROC/spAppWMS_WMSInfo',
      bodyReq: _preBodyReq,
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Reportes
  static DARequestModel repXCobrar({
    String? cliente,
    bool? detalle,
  }) {
    String uriRoute = "/PROC/spReportesPorCobrar";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
      bodyReq: {},
    );

    if (cliente != null) {
      _req.bodyReq!["Cliente"] = cliente;
    }

    if (detalle != null) {
      _req.bodyReq!["Detalle"] = detalle;
    }

    return _req;
  }

  static DARequestModel requestReport({
    required String reporte,
    String? cliente,
    required bool detalle,
  }) {
    String uriRoute = "/PROC/spQuiquin_Reportes";

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
      bodyReq: {
        "Reporte": reporte,
        "Cliente": cliente,
        "Detalle": detalle,
      },
    );

    return _req;
  }
}
