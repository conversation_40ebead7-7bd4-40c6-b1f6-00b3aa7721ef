import 'package:flutter/material.dart';

import '../DAPackagesRef.dart';
import 'app_config.dart';

class AppRoutes {
  static Route<dynamic> onGenerateRoute(
      BuildContext context, RouteSettings settings) {
    if (!DASessionProvider.toLogin && settings.name != '/home') {
      AppConfig.logOut();
      return MaterialPageRoute(
          builder: (_) => DALoginPage(
                appName: AppConfig.appName,
                licence: AppConfig.licence,
                idUsuarioTipo: AppConfig.idUsuarioTipo,
                loginLogo: AppConfig.loginLogo,
              ));
    }

    switch (settings.name) {
      case '/home':
        return MaterialPageRoute(builder: (_) => HomePage());
      case '/login':
        return MaterialPageRoute(
            builder: (_) => DALoginPage(
                  appName: AppConfig.appName,
                  licence: AppConfig.licence,
                  idUsuarioTipo: AppConfig.idUsuarioTipo,
                  loginLogo: AppConfig.loginLogo,
                ));
      default:
        return DAController.getDARoute(
          routeName: (settings.name) ?? '/home',
          settings: settings,
          defLogoAvatar: AppConfig.logoAvatar,
          appMenu: AppConfig.appMenu(context),
          defPage: HomePage(),
        );
    }
  }
}
