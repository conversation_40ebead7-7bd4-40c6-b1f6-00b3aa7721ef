import '/src/models/articuloOffline.dart';

class PedidoOffline {
  final int? id;
  final String? proyecto;
  final String moneda;
  final double tipoCambio;
  final String? listaPrecios;
  final DateTime fechaEmision;
  final String cliente;
  final String? referencia;
  final String? observaciones;
  final double? descuentoGlobal;
  final int? idCita;
  final String? enviarA;
  final String? condicionPago;
  final double? mapaLatitud;
  final double? mapaLongitud;
  final List<ArticuloOffline> detalle;

  PedidoOffline({
    this.id,
    this.proyecto,
    required this.moneda,
    required this.tipoCambio,
    this.listaPrecios,
    required this.fechaEmision,
    required this.cliente,
    this.referencia,
    this.observaciones,
    this.descuentoGlobal,
    this.idCita,
    this.enviarA,
    this.condicionPago,
    this.mapaLatitud,
    this.mapaLongitud,
    required this.detalle,
  });

  factory PedidoOffline.fromJson(Map<String, dynamic> json) {
    return PedidoOffline(
      id: json['ID'],
      proyecto: json['Proyecto'],
      moneda: json['Moneda'],
      tipoCambio: json['TipoCambio'],
      listaPrecios: json['ListaPrecios'],
      fechaEmision: DateTime.parse(json['FechaEmision']),
      cliente: json['Cliente'],
      referencia: json['Referencia'],
      observaciones: json['Observaciones'],
      descuentoGlobal: json['DescuentoGlobal'],
      idCita: json['IDCita'],
      enviarA: json['EnviarA'],
      condicionPago: json['CondicionPago'],
      mapaLatitud: json['MapaLatitud'],
      mapaLongitud: json['MapaLongitud'],
      detalle: (json['Detalle'] is List)
          ? (json['Detalle'] as List<dynamic>)
              .map((e) => ArticuloOffline.fromJson(e))
              .toList()
          : [ArticuloOffline.fromJson(json['Detalle'])],
    );
  }

  toJson() {
    return {
      'ID': id,
      'Proyecto': proyecto,
      'Moneda': moneda,
      'TipoCambio': tipoCambio,
      'ListaPrecios': listaPrecios,
      'FechaEmision': fechaEmision.toIso8601String(),
      'Cliente': cliente,
      'Referencia': referencia,
      'Observaciones': observaciones,
      'DescuentoGlobal': descuentoGlobal,
      'IDCita': idCita,
      'EnviarA': enviarA,
      'CondicionPago': condicionPago,
      'MapaLatitud': mapaLatitud,
      'MapaLongitud': mapaLongitud,
      'Detalle': detalle.map((e) => e.toJson()).toList(),
    };
  }
}
