class SucursalCliente {
  String? cliente;
  int? id;
  String? nombre;
  String? direccion;
  String? direccionNumero;
  String? direccionNumeroInt;
  String? entreCalles;
  String? plano;
  String? observaciones;
  String? colonia;
  String? delegacion;
  String? poblacion;
  String? estado;
  String? pais;
  String? zona;
  String? ruta;
  String? codigoPostal;
  String? telefonos;
  String? fax;
  bool? pedirTono;
  String? contacto1;
  String? contacto2;
  String? extencion1;
  String? extencion2;
  String? email1;
  String? email2;
  bool? emailAuto;
  String? zonaImpuesto;
  String? agente;
  String? clase;
  String? estatus;
  String? ultimoCambio;
  String? alta;
  String? mensaje;
  String? formaEnvio;
  String? listaPreciosEsp;
  String? proyecto;
  String? condicion;
  String? descuento;
  String? modificarVencimiento;
  String? categoria;
  String? grupo;
  String? familia;
  bool? tieneMovimientos;
  String? contrasena;
  String? clave;
  int? sucursalEmpresa;
  String? personalCobrador;
  String? almacen;
  String? almacenVtasConsignacion;
  String? wMovVentas;
  String? unidad;
  String? fiscalRegimen;
  String? fiscalZona;

  SucursalCliente() {
    cliente = '';
    id = 0;
    nombre = '';
    direccion = '';
    direccionNumero = '';
    direccionNumeroInt = '';
    entreCalles = '';
    plano = '';
    observaciones = '';
    colonia = '';
    delegacion = '';
    poblacion = '';
    estado = '';
    pais = '';
    zona = '';
    ruta = '';
    codigoPostal = '';
    telefonos = '';
    fax = '';
    pedirTono = false;
    contacto1 = '';
    contacto2 = '';
    extencion1 = '';
    extencion2 = '';
    email1 = '';
    email2 = '';
    emailAuto = false;
    zonaImpuesto = '';
    agente = '';
    clase = '';
    estatus = '';
    ultimoCambio = '';
    alta = '';
    mensaje = '';
    formaEnvio = '';
    listaPreciosEsp = '';
    proyecto = '';
    condicion = '';
    descuento = '';
    modificarVencimiento = '';
    categoria = '';
    grupo = '';
    familia = '';
    tieneMovimientos = false;
    contrasena = '';
    clave = '';
    sucursalEmpresa = 0;
    personalCobrador = '';
    almacen = '';
    almacenVtasConsignacion = '';
    wMovVentas = '';
    unidad = '';
    fiscalRegimen = '';
    fiscalZona = '';
  }

  SucursalCliente.fromJson(Map<String, dynamic> json) {
    cliente = json['Cliente'];
    id = json['ID'];
    nombre = json['Nombre'];
    direccion = json['Direccion'];
    direccionNumero = json['DireccionNumero'];
    direccionNumeroInt = json['DireccionNumeroInt'];
    entreCalles = json['EntreCalles'];
    plano = json['Plano'];
    observaciones = json['Observaciones'];
    colonia = json['Colonia'];
    delegacion = json['Delegacion'];
    poblacion = json['Poblacion'];
    estado = json['Estado'];
    pais = json['Pais'];
    zona = json['Zona'];
    ruta = json['Ruta'];
    codigoPostal = json['CodigoPostal'];
    telefonos = json['Telefonos'];
    fax = json['Fax'];
    pedirTono = json['PedirTono'];
    contacto1 = json['Contacto1'];
    contacto2 = json['Contacto2'];
    extencion1 = json['Extencion1'];
    extencion2 = json['Extencion2'];
    email1 = json['eMail1'];
    email2 = json['eMail2'];
    emailAuto = json['eMailAuto'];
    zonaImpuesto = json['ZonaImpuesto'];
    agente = json['Agente'];
    clase = json['Clase'];
    estatus = json['Estatus'];
    ultimoCambio = json['UltimoCambio'];
    alta = json['Alta'];
    mensaje = json['Mensaje'];
    formaEnvio = json['FormaEnvio'];
    listaPreciosEsp = json['ListaPreciosEsp'];
    proyecto = json['Proyecto'];
    condicion = json['Condicion'];
    descuento = json['Descuento'];
    modificarVencimiento = json['ModificarVencimiento'];
    categoria = json['Categoria'];
    grupo = json['Grupo'];
    familia = json['Familia'];
    tieneMovimientos = json['TieneMovimientos'];
    contrasena = json['Contrasena'];
    clave = json['Clave'];
    sucursalEmpresa = json['SucursalEmpresa'];
    personalCobrador = json['PersonalCobrador'];
    almacen = json['Almacen'];
    almacenVtasConsignacion = json['AlmacenVtasConsignacion'];
    wMovVentas = json['wMovVentas'];
    unidad = json['Unidad'];
    fiscalRegimen = json['FiscalRegimen'];
    fiscalZona = json['FiscalZona'];
  }

  SucursalCliente.fromSucursalCliente(SucursalCliente _) {
    cliente = _.cliente;
    id = _.id;
    nombre = _.nombre;
    direccion = _.direccion;
    direccionNumero = _.direccionNumero;
    direccionNumeroInt = _.direccionNumeroInt;
    entreCalles = _.entreCalles;
    plano = _.plano;
    observaciones = _.observaciones;
    colonia = _.colonia;
    delegacion = _.delegacion;
    poblacion = _.poblacion;
    estado = _.estado;
    pais = _.pais;
    zona = _.zona;
    ruta = _.ruta;
    codigoPostal = _.codigoPostal;
    telefonos = _.telefonos;
    fax = _.fax;
    pedirTono = _.pedirTono;
    contacto1 = _.contacto1;
    contacto2 = _.contacto2;
    extencion1 = _.extencion1;
    extencion2 = _.extencion2;
    email1 = _.email1;
    email2 = _.email2;
    emailAuto = _.emailAuto;
    zonaImpuesto = _.zonaImpuesto;
    agente = _.agente;
    clase = _.clase;
    estatus = _.estatus;
    ultimoCambio = _.ultimoCambio;
    alta = _.alta;
    mensaje = _.mensaje;
    formaEnvio = _.formaEnvio;
    listaPreciosEsp = _.listaPreciosEsp;
    proyecto = _.proyecto;
    condicion = _.condicion;
    descuento = _.descuento;
    modificarVencimiento = _.modificarVencimiento;
    categoria = _.categoria;
    grupo = _.grupo;
    familia = _.familia;
    tieneMovimientos = _.tieneMovimientos;
    contrasena = _.contrasena;
    clave = _.clave;
    sucursalEmpresa = _.sucursalEmpresa;
    personalCobrador = _.personalCobrador;
    almacen = _.almacen;
    almacenVtasConsignacion = _.almacenVtasConsignacion;
    wMovVentas = _.wMovVentas;
    unidad = _.unidad;
    fiscalRegimen = _.fiscalRegimen;
    fiscalZona = _.fiscalZona;
  }

  toJson() {
    return {
      'Cliente': cliente ?? '',
      'ID': id,
      'Sucursal': id ?? 0,
      'Nombre': nombre ?? '',
      'SucursalNombre': nombre ?? '',
      'Direccion': direccion ?? '',
      'DireccionNumero': direccionNumero ?? '',
      'DireccionNumeroInt': direccionNumeroInt ?? '',
      'EntreCalles': entreCalles ?? '',
      'Plano': plano ?? '',
      'Observaciones': observaciones ?? '',
      'Colonia': colonia ?? '',
      'Delegacion': delegacion ?? '',
      'Poblacion': poblacion ?? '',
      'Estado': estado ?? '',
      'Pais': pais ?? '',
      'Zona': zona ?? '',
      'Ruta': ruta ?? '',
      'CodigoPostal': codigoPostal ?? '',
      'Telefonos': telefonos ?? '',
      'Fax': fax ?? '',
      'PedirTono': pedirTono ?? false,
      'Contacto1': contacto1 ?? '',
      'Contacto2': contacto2 ?? '',
      'Extencion1': extencion1 ?? '',
      'Extencion2': extencion2 ?? '',
      'eMail1': email1 ?? '',
      'eMail2': email2 ?? '',
      'eMailAuto': emailAuto ?? false,
      'ZonaImpuesto': zonaImpuesto ?? '',
      'Agente': agente ?? '',
      'Clase': clase ?? '',
      'Estatus': estatus ?? '',
      'UltimoCambio': ultimoCambio ?? '',
      'Alta': alta ?? '',
      'Mensaje': mensaje ?? '',
      'FormaEnvio': formaEnvio ?? '',
      'ListaPreciosEsp': listaPreciosEsp ?? '',
      'Proyecto': proyecto ?? '',
      'Condicion': condicion ?? '',
      'Descuento': descuento ?? '',
      'ModificarVencimiento': modificarVencimiento ?? '',
      'Categoria': categoria ?? '',
      'Grupo': grupo ?? '',
      'Familia': familia ?? '',
      'TieneMovimientos': tieneMovimientos ?? false,
      'Contrasena': contrasena ?? '',
      'Clave': clave ?? '',
      'SucursalEmpresa': sucursalEmpresa ?? 0,
      'PersonalCobrador': personalCobrador ?? '',
      'Almacen': almacen ?? '',
      'AlmacenVtasConsignacion': almacenVtasConsignacion ?? '',
      'wMovVentas': wMovVentas ?? '',
      'Unidad': unidad ?? '',
      'FiscalRegimen': fiscalRegimen ?? '',
      'FiscalZona': fiscalZona ?? '',
      'EsSucursal': true,
    };
  }
}
