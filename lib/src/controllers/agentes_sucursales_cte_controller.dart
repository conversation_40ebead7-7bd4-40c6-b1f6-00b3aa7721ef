import 'package:get/get.dart';
import 'package:pedidosdipath/src/models/sucursalCliente.dart';
import '../app_config/api_request.dart';
import '../controllers/user_config_controller.dart';

class AgentesSucursalesCteController extends GetxController {
  static AgentesSucursalesCteController get to => Get.find();

  var isLoading = false.obs;
  var sucursalesList = <SucursalCliente>[].obs;
  var errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchSucursalesAgentes();
  }

  Future<void> fetchSucursalesAgentes() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final request = ApiRequest.getSucursalesAgentes(
          agente: Get.find<UserConfigController>().usuarioMovil.agente ?? '');
      final response =
          await ApiRequest.execAPI(request.uriReq, request.bodyReq);

      if (response != null && response is List) {
        sucursalesList.value =
            response.map((e) => SucursalCliente.fromJson(e)).toList();
      }
    } catch (e) {
      errorMessage.value =
          'Error al cargar sucursales de agentes: ${e.toString()}';
      print('Error en fetchSucursalesAgentes: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
