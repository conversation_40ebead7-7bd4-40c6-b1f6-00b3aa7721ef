import 'package:get/get.dart';
import 'package:pedidosdipath/src/models/sucursalCliente.dart';
import '../app_config/api_request.dart';
import '../controllers/user_config_controller.dart';

class AgentesSucursalesCteController extends GetxController {
  static AgentesSucursalesCteController get to => Get.find();

  var isLoading = false.obs;
  var sucursalesList = <SucursalCliente>[].obs;
  var errorMessage = ''.obs;
  var sucursalClienteSeleccionada = Rxn<SucursalCliente>();

  @override
  void onInit() {
    super.onInit();
    fetchSucursalesAgentes();
  }

  Future<void> fetchSucursalesAgentes() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final request = ApiRequest.getSucursalesAgentes(
          agente: Get.find<UserConfigController>().usuarioMovil.agente ?? '');
      final response =
          await ApiRequest.execAPI(request.uriReq, request.bodyReq);

      if (response != null && response is List) {
        sucursalesList.value =
            response.map((e) => SucursalCliente.fromJson(e)).toList();
      }
    } catch (e) {
      errorMessage.value =
          'Error al cargar sucursales de agentes: ${e.toString()}';
      print('Error en fetchSucursalesAgentes: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Obtiene las sucursales de un cliente específico
  List<SucursalCliente> getSucursalesCliente(String cliente) {
    return sucursalesList
        .where((sucursal) => sucursal.cliente == cliente)
        .toList();
  }

  /// Verifica si un cliente tiene sucursales disponibles
  bool clienteTieneSucursales(String cliente) {
    return getSucursalesCliente(cliente).isNotEmpty;
  }

  /// Convierte las sucursales del cliente a formato dropdown
  List<Map<String, dynamic>> sucursalesClienteToDropdown(String cliente) {
    final sucursalesCliente = getSucursalesCliente(cliente);
    return sucursalesCliente
        .map((sucursal) => {
              'value': sucursal,
              'label': '${sucursal.nombre ?? 'Sucursal ${sucursal.id}'}'
            })
        .toList();
  }

  /// Establece la sucursal del cliente seleccionada
  set setSucursalClienteSeleccionada(SucursalCliente sucursal) {
    sucursalClienteSeleccionada.value = sucursal;
  }

  /// Obtiene la sucursal del cliente seleccionada
  SucursalCliente? get getSucursalClienteSeleccionada =>
      sucursalClienteSeleccionada.value;

  /// Resetea la sucursal del cliente seleccionada
  void resetSucursalCliente() {
    sucursalClienteSeleccionada.value = null;
  }
}
