// Flutter Core
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

// Third-Party Packages
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Local Configurations & Utilities
import 'DAPackagesRef.dart';
import 'app_config/api_request.dart';
import 'app_config/app_config.dart';
import 'controllers/articles_icon_controller.dart';
import 'controllers/dipath_controller.dart';
import 'controllers/juego_controller.dart';
import 'controllers/opciones_controller.dart';
import 'controllers/sucursales_agente_controller.dart';
import 'controllers/sync_changes_controller.dart';
import 'controllers/sync_controller.dart';
import 'controllers/ticket_controller.dart';
import 'controllers/user_config_controller.dart';
import 'controllers/agentes_sucursales_cte_controller.dart';

import 'helpers.dart';
import 'prefs_manager.dart';

class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isLoading = true;
  late SharedPreferences _prefs;

  @override
  void initState() {
    super.initState();
    _initPrefs();
    loadSesion();
    DAController.refAvatar(context, () {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
    await _prefs.setBool('AppIsOffline', AppConfig.isOffline);
  }

  @override
  Widget build(BuildContext context) {
    // No borrar, es para logout
    Provider.of<DASessionProvider>(context);
    if (!DASessionProvider.toLogin) {
      AppConfig.logOut();
    }

    return LoadingOverlay(
      isLoading: (_isLoading),
      child: Scaffold(
        key: _scaffoldKey,
        body: CustomScrollView(
          slivers: <Widget>[
            DATitleBig(
              prefix: 'Intelisis',
              title: '',
            ),
          ],
        ),
        backgroundColor: Color.fromRGBO(244, 243, 243, 1),
      ),
    );
  }

  Future<void> loadSesion() async {
    try {
      // Descargamos data inicial
      DAUsuarioProvider usrProv = DAUsuarioProvider();
      DASessionProvider _eProv = DASessionProvider();
      bool result = await InternetConnectionChecker().hasConnection;
      if (result == true) {
        print('YAY! Free cute dog pics!');
        AppCfgModel appConfig =
            appCfgModelFromJson(await _eProv.getAppConfig(AppConfig.licence));
        await usrProv.setAppConfig(appConfig);
        await _eProv.setAgente();
        await getUserCfg();
        await getTipoCambio();

        await _prefs.setBool('AppIsOffline', AppConfig.isOffline);
      } else {
        print('No internet');
        await loadMonedaFromPrefs();
        await loadTipoCambioFromPrefs();
      }
      /* AppCfgModel appConfig =
          appCfgModelFromJson(await _eProv.getAppConfig(AppConfig.licence));
      await usrProv.setAppConfig(appConfig);
      await _eProv.setAgente();
      await getUserCfg(); */
      if (!Get.find<SyncController>().getSynced) {
        bool result = await InternetConnectionChecker().hasConnection;
        if (result == true) {
          await PrefsManager.clearPrefs();
          await loadArticulos();
          await loadJuegos();
          await loadJuegosD();
          await loadOpciones();
          await loadCitas();
          await loadCatalogos();
          await loadClientes();
          await loadHistorialCobros();
          await loadHistorialPedidos();
          await loadArticlesIcons();
          await getSyncChanges();
          await loadConfiguracionPrecios();
          await getZonaFronteriza();
          await loadSucursalesAgente();
          await Get.find<AgentesSucursalesCteController>().fetchSucursalesAgentes();
        }
        Get.find<SyncController>().setSynced = true;
        _isLoading = false;
        setState(() {});

        if (result == false) {
          _prefs.setBool('AppUsedOffline', true);
          gotoHome(null);
          _isLoading = false;
          setState(() {});
          return;
        }
        gotoHome(null);
      }

      _isLoading = false;
      setState(() {});

      gotoHome(null);
    } catch (e) {
      print(e.toString());
      bool usedOffline = _prefs.getBool('AppUsedOffline') ?? false;
      Fluttertoast.showToast(
          msg: usedOffline
              ? 'Se ha detectado un ingreso a la aplicación fuera de línea y se ha detectado conexión a Internet, es requerido volver a iniciar sesión.'
              : e.toString(),
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.SNACKBAR,
          timeInSecForIosWeb: 3,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          fontSize: 16.0);
      _prefs.setBool('AppUsedOffline', false);
      AppConfig.logOut();
      setState(() {});
    }
  }

  Future<void> getUserCfg() async {
    // Enviamos el request
    DARequestModel _req = ApiRequest.userConfig();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    // Guardamos la configuración del usuario
    if (data != null) {
      Get.find<UserConfigController>().setUsuarioMovil = data[0];
      PrefsManager.setMonedaUsuario(
          Get.find<UserConfigController>().monedaUsuario ?? '');
    }
  }

  Future<void> getTipoCambio() async {
    // Enviamos el request
    DARequestModel _req = ApiRequest.obtenerTipoCambio;
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    if (data != null) {
      double _tipoCambio = data[0]['TipoCambio'];
      Get.find<UserConfigController>().setTipoCambio = _tipoCambio;
      PrefsManager.setTipoCambioUsuario(_tipoCambio);
    }
  }

  Future<void> loadMonedaFromPrefs() async {
    String? moneda = await PrefsManager.getMonedaUsuario();
    if (moneda != null && moneda.isNotEmpty) {
      Get.find<UserConfigController>().setMonedaBase = moneda;
    }
  }

  Future<void> loadTipoCambioFromPrefs() async {
    double? tipoCambio = await PrefsManager.getTipoCambioUsuario();
    if (tipoCambio != null) {
      Get.find<UserConfigController>().setTipoCambio = tipoCambio;
    }
  }

  Future<void> loadArticulos() async {
    if (kDebugMode) {
      String? articulos = await PrefsManager.getArticulos();
      if (articulos != null && articulos.isNotEmpty) {
        return;
      }
    }

    List<dynamic> arts = [];
    var artsFast = [];

    DARequestModel _req = ApiRequest.getArticulosFast();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    if (data != null) {
      artsFast = transformDataOptimizedV3(data);
    }

    for (int i = 0; i < artsFast.length; i++) {
      dynamic art = artsFast[i];
      if (art != null) {
        if (art["PrecioLista"] == null || art["PrecioLista"] == 0) {
          if (art['ListaPrecios'] != null) {
            // Validamos si es un mapa o una lista
            if (art['ListaPrecios'] is Map) {
              dynamic precio = art['ListaPrecios']['Precio'];
              if (precio == null || precio == 0) {
              } else {
                // Agregamos existencias
                dynamic disponibleMap = art['Disponible'];
                if (disponibleMap != null) {
                  if (disponibleMap is Map) {
                    art['Existencia'] = disponibleMap['Disponible'].toString();
                  } else if (disponibleMap is List) {
                    // Si es una lista, buscamos que haya un match con dispAlmacen
                    for (int i = 0; i < disponibleMap.length; i++) {
                      if (disponibleMap[i]['Almacen'] ==
                          Get.find<UserConfigController>()
                              .usuarioMovil
                              .dispAlmacen) {
                        art['Existencia'] =
                            disponibleMap[i]['Disponible'].toString();
                        break;
                      }
                    }
                  }
                }
                art['checked'] = false;
                // Agregamos el articulo
                arts.add(art);
              }
            } else if (art["ListaPrecios"][0]['Precio'] == null ||
                art["ListaPrecios"][0]['Precio'] == 0) {
            } else {
              // Agregamos existencias
              dynamic disponibleMap = art['Disponible'];
              if (disponibleMap != null) {
                if (disponibleMap is Map) {
                  art['Existencia'] = disponibleMap['Disponible'].toString();
                } else if (disponibleMap is List) {
                  // Si es una lista, buscamos que haya un match con dispAlmacen
                  for (int i = 0; i < disponibleMap.length; i++) {
                    if (disponibleMap[i]['Almacen'] ==
                        Get.find<UserConfigController>()
                            .usuarioMovil
                            .dispAlmacen) {
                      art['Existencia'] =
                          disponibleMap[i]['Disponible'].toString();
                      break;
                    }
                  }
                }
              }
              art['checked'] = false;
              // Agregamos el articulo
              arts.add(art);
            }
          }
        } else {
          // Agregamos existencias
          dynamic disponibleMap = art['Disponible'];
          if (disponibleMap != null) {
            if (disponibleMap is Map) {
              art['Existencia'] = disponibleMap['Disponible'].toString();
            } else if (disponibleMap is List) {
              // Si es una lista, buscamos que haya un match con dispAlmacen
              for (int i = 0; i < disponibleMap.length; i++) {
                if (disponibleMap[i]['Almacen'] ==
                    Get.find<UserConfigController>().usuarioMovil.dispAlmacen) {
                  art['Existencia'] = disponibleMap[i]['Disponible'].toString();
                  break;
                }
              }
            }
          }
          art['checked'] = false;
          // Agregamos el articulo
          arts.add(art);
        }
      }
    }

    // Validamos que si la existencia es null, la agregamos como -
    arts.forEach((element) {
      if (element['Existencia'] == null || element['Existencia'] == 'null') {
        element['Existencia'] = '-';
      }
    });

    PrefsManager.setArticulos(articulos: json.encode(arts));
    PrefsManager.setUltimaSincronizacion();
  }

  Future<void> loadJuegos() async {
    if (kDebugMode) {
      String? juegos = await PrefsManager.getJuegos();
      if (juegos != null && juegos.isNotEmpty) {
        return;
      }
    }
    DARequestModel _req = ApiRequest.getJuegos();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
    PrefsManager.setJuegos(juegos: json.encode(data));

    JuegoController juegoController = Get.find<JuegoController>();
    juegoController.reset(isSync: true);
    data.forEach((element) {
      juegoController.setArtJuego(element);
    });
    juegoController.setArtJuegoCopy();
  }

  Future<void> loadJuegosD() async {
    if (kDebugMode) {
      String? juegos = await PrefsManager.getJuegosD();
      if (juegos != null && juegos.isNotEmpty) {
        return;
      }
    }
    DARequestModel _req = ApiRequest.getJuegosD();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
    PrefsManager.setJuegosD(juegosD: json.encode(data));

    JuegoController juegoController = Get.find<JuegoController>();
    data.forEach((element) {
      juegoController.setArtJuegoD(element);
    });
    juegoController.setArtJuegoDCopy();
  }

  Future<void> loadOpciones() async {
    if (kDebugMode) {
      String? opciones = await PrefsManager.getOpciones();
      if (opciones != null && opciones.isNotEmpty) {
        return;
      }
    }

    List<dynamic> opcs = [];

    DARequestModel _req = ApiRequest.getOpciones();

    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    for (int i = 0; i < data.length; i++) {
      dynamic opc = data[i];
      opcs.add(opc);
    }

    PrefsManager.setOpciones(opciones: json.encode(opcs));

    OpcionesController _opcionesController = Get.find<OpcionesController>();
    _opcionesController.reset();
    _opcionesController.init();
  }

  Future<void> loadCitas() async {
    if (kDebugMode) {
      String? citas = await PrefsManager.getCitas();
      if (citas != null && citas.isNotEmpty) {
        return;
      }
    }

    DARequestModel _req = ApiRequest.getCitas();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    PrefsManager.setCitas(citas: json.encode(data));
  }

  Future<void> loadCatalogos() async {
    if (kDebugMode) {
      String? catalogos = await PrefsManager.getCatalogos();
      if (catalogos != null && catalogos.isNotEmpty) {
        return;
      }
    }

    DARequestModel _req = ApiRequest.getCatalogos();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    PrefsManager.setCatalogos(catalogos: json.encode(data));
  }

  Future<void> loadClientes() async {
    if (kDebugMode) {
      String? clientes = await PrefsManager.getClientes();
      if (clientes != null && clientes.isNotEmpty) {
        return;
      }
    }

    DARequestModel _req = ApiRequest.getClientes();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    PrefsManager.setClientes(clientes: json.encode(data));
  }

  Future<void> loadHistorialCobros() async {
    if (kDebugMode) {
      List<String>? historialCobros =
          await PrefsManager.getServerHistorialCobros();
      if (historialCobros != null && historialCobros.isNotEmpty) {
        return;
      }
    }

    DARequestModel _reqHistCobros = ApiRequest.historialCobro();
    dynamic dataHistCobro =
        await ApiRequest.execAPI(_reqHistCobros.uriReq, _reqHistCobros.bodyReq);

    PrefsManager.setServerHistorialCobros(historial: dataHistCobro['Cobros']);
  }

  Future<void> loadHistorialPedidos() async {
    if (kDebugMode) {
      List<String>? historialPedidos =
          await PrefsManager.getServerHistorialPedidos();
      if (historialPedidos != null && historialPedidos.isNotEmpty) {
        return;
      }
    }

    DARequestModel _req = ApiRequest.historial();
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    PrefsManager.setServerHistorialPedidos(historial: data['Pedidos']);
  }

  Future<void> loadArticlesIcons() async {
    ArticleIconController _iconsController = Get.find<ArticleIconController>();

    String tempArticles = await PrefsManager.getArticulos();
    List<dynamic> articulos = json.decode(tempArticles);
    articulos.forEach((element) {
      _iconsController.add(element['Tipo'].toString());
    });
  }

  Future<void> getSyncChanges() async {
    SyncChangesController _syncChangesController =
        Get.find<SyncChangesController>();

    List<String>? changes = await PrefsManager.getCambiosClientes();

    if (changes != null && changes.isNotEmpty) {
      _syncChangesController.setHasChanges(true);
      changes.forEach((element) {
        _syncChangesController.add(element);
      });
    } else {
      _syncChangesController.setHasChanges(false);
    }
  }

  Future<void> loadConfiguracionPrecios() async {
    TicketController _ticketController = Get.find<TicketController>();

    // Carga configuración de precios con impuesto
    DARequestModel _reqPrecios = ApiRequest.getConfiguracionPrecios();
    dynamic dataPrecios =
        await ApiRequest.execAPI(_reqPrecios.uriReq, _reqPrecios.bodyReq);

    PrefsManager.setPrecioConImpuesto(
        dataPrecios[0]['VentaPreciosImpuestoIncluido']);
    _ticketController
        .setPrecioConImpuesto(dataPrecios[0]['VentaPreciosImpuestoIncluido']);

    // Carga tipo de impuesto
    DARequestModel _reqTipoImp = ApiRequest.getTipoImpuesto();
    dynamic dataTipoImp =
        await ApiRequest.execAPI(_reqTipoImp.uriReq, _reqTipoImp.bodyReq);

    if (dataTipoImp != null && dataTipoImp.length > 0) {
      bool tipoImpuesto = dataTipoImp[0]['TipoImpuesto'] == 1;
      PrefsManager.setTipoImpuesto(tipoImpuesto);
      _ticketController.setTipoImpuesto(tipoImpuesto);
    }
  }

  Future<void> getZonaFronteriza() async {
    DARequestModel _req = ApiRequest.obtenerZonaFronteriza;
    dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
    PrefsManager.setZonaFronteriza(data[0]['ZonaImpuesto'] ?? '');
    Get.find<DipathController>().setZonaImpuesto = data[0]['ZonaImpuesto'];
  }

  Future<void> loadSucursalesAgente() async {
    SucursalesAgenteController _sucursalesController =
        Get.find<SucursalesAgenteController>();
    try {
      DARequestModel _req = ApiRequest.obtenerSucursalesAgente();
      dynamic data = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

      if (data != null) {
        _sucursalesController.reset();
        for (var item in data) {
          if (item['Sucursal'] != null) {
            _sucursalesController.addSucursal(item['Sucursal']);
          }
        }
      }
    } catch (e) {
      print('Error cargando sucursales: $e');
    }
  }

  FutureOr gotoHome(Object? value) {
    Navigator.pushNamed(
      //Navigator.pushReplacementNamed(
      context,
      AppConfig.homeLayout,
      arguments: AppConfig.homeLayoutArgs(context),
    ).then(gotoHome);
  }
}
