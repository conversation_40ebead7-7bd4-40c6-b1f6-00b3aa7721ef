formatEstatus(String _) {
  switch (_.toUpperCase().trim()) {
    case 'PENDIENTE':
      return 'Pendiente';
    case 'CONCLUIDO':
      return 'Concluido';
    case 'CANCELADO':
      return 'Cancelado';
    case 'SINAFECTAR':
      return 'Sin Afectar';
    default:
      return 'Desconocido';
  }
}

rfcValido(String rfc, [bool aceptarGenerico = true]) {
  // Expresión regular para el RFC de Mexico
  RegExp exp = new RegExp(
      r'^([A-ZÑ&]{3,4}([0-9]{2})(0[1-9]|1[0-2])(0[1-9]|1[0-9]|2[0-9]|3[0-1]))([A-Z\d]{3})?$');
  // Verificamos si el RFC coincide con la expresión regular
  var validado = exp.hasMatch(rfc.trim().toUpperCase());
  if (!validado)
    return false;
  else {
    return true;
  }
}

telefonoValido(String telefono) {
  // Expresión regular para los números de teléfono de México
  RegExp exp = new RegExp(r'^[0-9]{10}$');
  var validado = exp.hasMatch(telefono.trim());
  if (!validado)
    return false;
  else {
    return true;
  }
}

emailValido(String email) {
  // Expresión regular para los emails
  RegExp exp = new RegExp(r'^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$');
  var validado = exp.hasMatch(email.trim());
  if (!validado)
    return false;
  else {
    return true;
  }
}

codigoPostalValido(String codigoPostal) {
  // Expresión regular para los códigos postales de México
  RegExp exp = new RegExp(r'^[0-9]{5}$');
  var validado = exp.hasMatch(codigoPostal.trim());
  if (!validado)
    return false;
  else {
    return true;
  }
}

String formatoMonetarioMexico(num cantidad) {
  return "\$ ${cantidad.toStringAsFixed(2).replaceAllMapped(new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}";
}

// Función para eliminar elementos de una lista que no están en una lista
void removerDeLista(List<dynamic> list, List<dynamic> listToRemove) {
  list.removeWhere((element) => !listToRemove.contains(element['Articulo']));
}

List<Map<String, dynamic>> transformDataOptimized(
  Map<String, dynamic> data, {
  String? articulo,
}) {
  // Extraemos las listas; si alguna es nula, usamos lista vacía.
  final List<dynamic> articulos = data['Articulos'] ?? [];
  final List<dynamic> disponibles = data['Disponibles'] ?? [];
  final List<dynamic> subDisponibles = data['SubDisponibles'] ?? [];

  // Creamos un mapa para búsquedas rápidas en SubDisponibles.
  // Usamos la clave compuesta concatenando Articulo, Empresa y Almacen.
  final Map<String, Map<String, dynamic>> subDispMap = {};
  for (var sd in subDisponibles) {
    final key = "${sd['Articulo']}_${sd['Empresa']}_${sd['Almacen']}";
    subDispMap[key] = sd;
  }

  final List<Map<String, dynamic>> result = [];

  // Iteramos cada artículo
  for (var art in articulos) {
    // Si se especifica un artículo y no coincide, se salta
    if (articulo != null && art['Articulo'] != articulo) continue;

    // Copia del artículo para mantener el original
    final Map<String, dynamic> newArt = Map<String, dynamic>.from(art);

    final List<Map<String, dynamic>> availableList = [];

    // Iteramos la lista de disponibles y filtramos por el artículo.
    for (var disp in disponibles) {
      if (disp['Articulo'] == art['Articulo']) {
        // Copiamos el objeto disponible
        final Map<String, dynamic> dispMap = Map<String, dynamic>.from(disp);
        // Construimos la clave para buscar en el mapa optimizado
        final key = "${disp['Articulo']}_${disp['Empresa']}_${disp['Almacen']}";
        // Agregamos el subdisponible encontrado (o null si no existe)
        dispMap['SubDisponibles'] = subDispMap[key];
        availableList.add(dispMap);
      }
    }

    // Se añade la lista de disponibles al artículo
    newArt['Disponible'] = availableList;
    result.add(newArt);
  }

  return result;
}

List<Map<String, dynamic>> transformDataOptimizedV3(
  Map<String, dynamic> data, {
  String? articulo,
}) {
  final subDispMap = <String, Map<String, dynamic>>{};
  final dispMap = <String, List<Map<String, dynamic>>>{};

  // 1. Preprocesamiento combinado en un solo loop
  for (final sd in (data['SubDisponibles'] as List<dynamic>? ?? [])) {
    subDispMap["${sd['Articulo']}_${sd['Empresa']}_${sd['Almacen']}"] =
        Map<String, dynamic>.from(sd);
  }

  // 2. Procesamiento de Disponibles con SubDisponibles incorporados
  for (final disp in (data['Disponibles'] as List<dynamic>? ?? [])) {
    final key = disp['Articulo'] as String;
    final sdKey = "${disp['Articulo']}_${disp['Empresa']}_${disp['Almacen']}";

    final mergedDisp = {
      ...disp as Map<String, dynamic>,
      'SubDisponibles': subDispMap[sdKey],
    };

    dispMap.putIfAbsent(key, () => []).add(mergedDisp);
  }

  // 3. Procesamiento final de Artículos con acceso directo
  return (data['Articulos'] as List<dynamic>? ?? [])
      .where((art) => articulo == null || art['Articulo'] == articulo)
      .map<Map<String, dynamic>>((art) {
    final artKey = art['Articulo'] as String;
    return {
      ...art as Map<String, dynamic>,
      'Disponible': dispMap[artKey] ?? [],
    };
  }).toList();
}

extension StringNumberFormat on String {
  String toFormattedNumber() {
    // Validamos que la cadena tenga el formato numérico
    final validNumber = RegExp(r'^-?\d+(\.\d+)?$');
    if (!validNumber.hasMatch(this)) {
      return this;
    }

    // Se extrae el signo negativo, si existe
    final bool isNegative = startsWith('-');
    final String numberStr = isNegative ? substring(1) : this;

    // Se separa la parte entera y la parte decimal
    final parts = numberStr.split('.');
    final String integerPart = parts[0];

    // Insertamos separadores de miles a la parte entera
    final String formattedInteger = integerPart.replaceAllMapped(
      RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );

    // Si existe parte decimal y no está vacía...
    if (parts.length > 1 && parts[1].isNotEmpty) {
      // Verificamos si la parte decimal contiene solo ceros
      if (parts[1].replaceAll('0', '').isEmpty) {
        // Si son solo ceros, no se muestra la parte decimal
        return (isNegative ? '-' : '') + formattedInteger;
      } else {
        // Si tiene valores distintos de 0, se conserva la parte decimal original
        return (isNegative ? '-' : '') + formattedInteger + '.' + parts[1];
      }
    } else {
      // No hay parte decimal
      return (isNegative ? '-' : '') + formattedInteger;
    }
  }
}

extension StringNumberFormatMoney on String {
  String toFormattedMoney() {
    try {
      // Try to parse the string as a number
      final number = double.parse(this);

      // Get the string with 2 decimals first
      String formatted = number.toStringAsFixed(2);

      // Add thousand separators
      return formatted.replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
      );
    } catch (e) {
      // Return original string if it can't be parsed as a number
      return this;
    }
  }
}
