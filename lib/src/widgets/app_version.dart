import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:yaml/yaml.dart';

Future<String> getAppVersion() async {
  try {
    String pubspecYaml = await rootBundle.loadString('pubspec.yaml');
    YamlMap pubspec = loadYaml(pubspecYaml);
    String version = pubspec['version'];
    return version;
  } catch (e) {
    print('Error al obtener la versión: $e');
    return 'Error al obtener la versión';
  }
}

class VersionDisplay extends StatefulWidget {
  @override
  _VersionDisplayState createState() => _VersionDisplayState();
}

class _VersionDisplayState extends State<VersionDisplay> {
  String _appVersion = '';
  @override
  void initState() {
    super.initState();
    getAppVersion().then((version) {
      setState(() {
        _appVersion = validarCompilacion(version);
      });
    });
  }

  String validarCompilacion(String cadena) {
    int index = cadena.indexOf('+');
    return (index != -1) ? cadena.substring(0, index) : cadena;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        'Versión: $_appVersion',
        style: TextStyle(fontSize: 14, color: Colors.grey),
      ),
    );
  }
}
