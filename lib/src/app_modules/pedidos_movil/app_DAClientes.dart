import 'package:badges/badges.dart' as badges;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:pedidosdipath/src/controllers/dipath_controller.dart';

import '/src/DAPackagesRef.dart';
import '/src/app_config/api_request.dart';
import '/src/app_modules/pedidos_movil/app_DACobro.dart';
import '/src/app_modules/pedidos_movil/app_DANuevoCliente.dart';
import '/src/controllers/cliente_controller.dart';
import '/src/controllers/lista_precios_controller.dart';
import '/src/controllers/ticket_controller.dart';
import '/src/controllers/sucursales_agente_controller.dart';
import '/src/helpers.dart';
import '/src/prefs_manager.dart';
import 'app_DAAgenda.dart';
import 'app_DACarrito.dart';

class AppDAClientes {
  /* Lista de clientes */
  static index(BuildContext context) {
    String _refID = 'clientes';
    DALayoutListCardProvider _layoutListProv =
        DALayoutListCardQueueProv.getByRefID(_refID);

    FutureOr onGoBack(Object? value) {
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();
    }

    ListaPreciosController _listaPreciosController =
        Get.find<ListaPreciosController>();

    TicketController _ticketController = Get.find<TicketController>();

    removeNull(String? value) {
      if (value == null || value == 'null') {
        return '';
      }
      return value;
    }

    DALayoutListModel modelLayout = new DALayoutListModel(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Clientes',
      hintText: 'Buscar...',
      noDataMsg: 'clientes',
      hasBackButton: false,
      floatingActionButton: DAFloatingActionButton(
        icon: Icons.add,
        heroTag: 'nuevoClienteScreen',
        onPressed: () {
          Navigator.pushNamed(context, '/WidgetsForm',
                  arguments: AppDANuevoCliente.index(context))
              .then(onGoBack);
        },
      ),
      cardConfig: DADefListModel(
        id: "ID",
        title: "Nombre",
        subtitle: "Direccion",
        leading: "Cliente",
        trailing: "RFC",
        icon: "man",
        onCardTap: (value) async {
          dynamic args = json.decode(value.metadata);

          // Obtenemos el controlador de sucursales
          final _sucursalesController = Get.find<SucursalesAgenteController>();

          // Si solo hay una sucursal, la seleccionamos automáticamente
          if (_sucursalesController.sucursales.length == 1) {
            _sucursalesController.setSucursalSeleccionada =
                _sucursalesController.sucursales[0];
          }
          // Si hay más de una sucursal, mostramos el modal de selección
          else if (_sucursalesController.sucursales.length > 1) {
            bool? continuar = await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: Text('Seleccionar Sucursal'),
                  content: GetBuilder<SucursalesAgenteController>(
                    builder: (_) => DropdownButtonFormField<num>(
                      decoration: InputDecoration(
                        labelText: 'Sucursal',
                        border: OutlineInputBorder(),
                      ),
                      value: _.sucursalSeleccionada,
                      items: _
                          .sucursalesToDropdown()
                          .map((item) => DropdownMenuItem<num>(
                                value: item['value'],
                                child: Text(item['label']),
                              ))
                          .toList(),
                      onChanged: (value) {
                        _.setSucursalSeleccionada = value!;
                      },
                      validator: (value) => value == null
                          ? 'Por favor seleccione una sucursal'
                          : null,
                    ),
                  ),
                  actions: [
                    TextButton(
                      child: Text('Cancelar'),
                      onPressed: () => Navigator.of(context).pop(false),
                    ),
                    ElevatedButton(
                      child: Text('Continuar'),
                      onPressed: () {
                        if (_sucursalesController.sucursalSeleccionada !=
                            null) {
                          Navigator.of(context).pop(true);
                        }
                      },
                    ),
                  ],
                );
              },
            );

            // Si canceló o no seleccionó sucursal, no continuamos
            if (continuar != true) return;
          }
          // Si no hay sucursales disponibles, mostramos un mensaje y no continuamos
          else {
            Fluttertoast.showToast(
                msg: 'No hay sucursales disponibles',
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.red,
                textColor: Colors.white);
            return;
          }

          String _direccionCliente =
              '${removeNull(args['Direccion'])} ${removeNull(args['DireccionNumero'])} ${removeNull(args['DireccionNumeroInt'])}\n${removeNull(args['Colonia'])}\n${removeNull(args['Poblacion'])}\n${removeNull(args['Estado'])}\n${removeNull(args['CodigoPostal'])}';
          String _telefonoCliente =
              '${removeNull(args['TelefonosLada'])} ${removeNull(args['Telefonos'])}';
          String _emailCliente = '${removeNull(args['eMail1'])}';
          _ticketController.setDataCliente(args['Nombre'], _direccionCliente,
              _telefonoCliente, _emailCliente);

          Get.find<DipathController>().setCliente = args['Cliente'];
          await PrefsManager.setClienteActual(
              clienteActual: args['Cliente'].toString());

          // Verificamos si tiene lista de precios
          _listaPreciosController.resetListaPrecios();
          if (args['ListaPrecios'] != null &&
              args['ListaPreciosEsp'].toString().isNotEmpty) {
            if (args['ListaPreciosEsp'] != '(Precio Lista)') {
              _listaPreciosController.setListaPrecios(args['ListaPreciosEsp']);
            }
          }

          // Agregamos condicion de pago del cliente
          Get.find<ClienteController>().reset();
          Get.find<ClienteController>().setCondicionPago = args['Condicion'];

          // Verificamos si el cliente ya tiene un carrito
          List<String>? tempCart = await PrefsManager.getCarrito();
          String _clienteActual = await PrefsManager.getClienteActual();

          List<dynamic> _carrito = [];

          if (tempCart != null && tempCart.isNotEmpty) {
            tempCart.forEach(
              (element) {
                dynamic articulo = json.decode(element);
                if (articulo['Cliente'] == _clienteActual) {
                  _carrito.add(articulo);
                }
              },
            );
          }

          bool tieneCarrito = _carrito.isEmpty ? false : true;
          int cantidadCarrito = _carrito.length;

          Navigator.pushNamed(
            context,
            '/WidgetsForm',
            arguments: AppDAClientes.informacion(
              context,
              args,
              tieneCarrito: tieneCarrito,
              cantidadCarrito: cantidadCarrito,
            ),
          ).then(onGoBack);
        },
      ),
      bottomNavigationBar: DABottomAppBar(
        children: [
          TextButton.icon(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: () {},
            icon: Icon(Icons.people),
            label: Text(
              'Clientes',
              style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold),
            ),
          ),
          TextButton.icon(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: () {
              Navigator.pushNamed(
                context,
                '/ListPage',
                arguments: AppDAAgenda.index(
                  context,
                ),
              ).then(onGoBack);
            },
            icon: Icon(
              Icons.calendar_month,
              size: 18,
              color: Colors.black.withValues(alpha: 0.25),
            ),
            label: Text(
              'Agenda',
              style: TextStyle(color: Colors.black.withValues(alpha: 0.25)),
            ),
          ),
          DABottomAppBarButton(
            label: '                    ',
            onTap: null,
          ),
        ],
      ),
    );

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];
      List<dynamic> clientes = [];
      try {
        String tempClientes = await PrefsManager.getClientes();
        if (tempClientes.contains('OkRef')) {
          dynamic jsonClientes = json.decode(tempClientes);
          String okRef = jsonClientes['OkRef'];
          Fluttertoast.showToast(
              msg: okRef,
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 3,
              backgroundColor: Colors.black,
              textColor: Colors.white,
              fontSize: 16.0);
        } else {
          dynamic tempJsonClientes = json.decode(tempClientes);
          clientes = tempJsonClientes['Clientes'];
          if (kDebugMode) {
            // Dejamos unicamente al cliente C013189
            clientes.removeWhere((element) => element['Cliente'] != 'C013189');

            // Print de cada cliente
            clientes.forEach((element) {
              print(element);
            });
          }
        }
      } catch (e) {
        Fluttertoast.showToast(
            msg: e.toString(),
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
            timeInSecForIosWeb: 3,
            backgroundColor: Colors.black,
            textColor: Colors.white,
            fontSize: 16.0);
      }

      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.data = clientes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }

  /* Informacion de Cliente */
  static informacion(BuildContext context, dynamic data,
      {required bool tieneCarrito, int cantidadCarrito = 0}) {
    String _refID = 'infoCliente|${data['Cliente']}';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    FutureOr onCarritoBack(Object? value) {
      Fluttertoast.showToast(
          msg: 'Pedido Actual Guardado',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          fontSize: 16.0);
    }

    try {
      DALayoutFormFilters _modelLayoutInfoCliente = new DALayoutFormFilters(
        heroTag: 'informacionClienteScreen',
        refID: _refID,
        prefix: '${data['Nombre']}',
        title: '${data['RFC']}',
        hasBackButton: true,
        iconSubmit:
            tieneCarrito ? Icons.shopping_cart : Icons.add_shopping_cart,
        formSubmit: (value) async {
          Navigator.pushNamed(
            context,
            '/WidgetsForm',
            arguments: AppDACarrito.index(
              context,
              data,
            ),
          ).then(onCarritoBack);
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(
          children: [
            DABottomAppBarButton(
              label: 'Editar',
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments: AppDAClientes.editar(
                    context,
                    data,
                  ),
                );
              },
              icon: Icons.edit,
            ),
            DABottomAppBarButton(
              icon: Icons.history,
              label: 'Historial',
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/ListPage',
                  arguments: AppDACobro.index(
                    context,
                    data,
                  ),
                );
              },
            ),
            tieneCarrito
                ? badges.Badge(
                    badgeStyle: badges.BadgeStyle(
                      badgeColor: Theme.of(context).primaryColor,
                    ),
                    badgeContent: Text(
                      cantidadCarrito.toString(),
                      style: TextStyle(color: Colors.white, fontSize: 32),
                    ),
                    child: SizedBox(
                      height: 2.5,
                      child: DABottomAppBarButton(
                        label: '               ',
                        onTap: null,
                      ),
                    ),
                  )
                : DABottomAppBarButton(
                    label: '               ',
                    onTap: null,
                  ),
          ],
        ),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [
          DAInput(
            refID: 'condicionPago',
            controller:
                new TextEditingController(text: data['Condicion'].toString()),
            label: 'Condición de Pago',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.textarea,
          ),
          DAInput(
            refID: 'saldo',
            controller:
                new TextEditingController(text: data['Saldo'].toString()),
            label: 'Saldo',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
          DAInput(
            refID: 'limiteCredito',
            controller: new TextEditingController(
                text: data['LimiteCreditoMN'].toString()),
            label: 'Límite de Crédito',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
          DAInput(
            refID: 'contacto',
            controller:
                new TextEditingController(text: data['Contacto1'].toString()),
            label: 'Contacto',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
          DAInput(
            refID: 'direccion',
            controller:
                new TextEditingController(text: data['Direccion'].toString()),
            label: 'Dirección',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
        ];
        return _formBody;
      }

      // Descargamos Catálogos y asignamos valores default
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutInfoCliente;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }

  /* Informacion Cliente */
  static informacionFromCarrito(
    BuildContext context,
    dynamic data,
  ) {
    String _refID = 'infoCliente|${data['Cliente']}';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    try {
      DALayoutFormFilters _modelLayoutInfoCliente = new DALayoutFormFilters(
        heroTag: 'informacionClienteScreen',
        refID: _refID,
        prefix: '${data['Nombre']}',
        title: '${data['RFC']}',
        hasBackButton: false,
        iconSubmit: Icons.done,
        formSubmit: (value) async {
          Navigator.of(context).pop();
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(
          children: [],
        ),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [
          DAInput(
            refID: 'condicionPago',
            controller:
                new TextEditingController(text: data['Condicion'].toString()),
            label: 'Condición de Pago',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.textarea,
          ),
          DAInput(
            refID: 'saldo',
            controller:
                new TextEditingController(text: data['Saldo'].toString()),
            label: 'Saldo',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
          DAInput(
            refID: 'limiteCredito',
            controller: new TextEditingController(
                text: data['LimiteCreditoMN'].toString()),
            label: 'Límite de Crédito',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
          DAInput(
            refID: 'contacto',
            controller:
                new TextEditingController(text: data['Contacto1'].toString()),
            label: 'Contacto',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
          DAInput(
            refID: 'direccion',
            controller:
                new TextEditingController(text: data['Direccion'].toString()),
            label: 'Dirección',
            isRequired: false,
            disabled: true,
            tipo: DAInputType.string,
          ),
        ];
        return _formBody;
      }

      // Descargamos Catálogos y asignamos valores default
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutInfoCliente;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }

  /* Editar Cliente */
  static editar(
    BuildContext context,
    dynamic data,
  ) {
    dynamic _data = data;
    String _refID = 'editCliente|${data['Cliente']}';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    TextEditingController _contactoController =
        new TextEditingController(text: data['Contacto1'].toString());
    TextEditingController _telefonoController =
        new TextEditingController(text: data['Telefonos'].toString());
    TextEditingController _emailController =
        new TextEditingController(text: data['eMail1'].toString());
    TextEditingController _direccionController =
        new TextEditingController(text: data['Direccion'].toString());
    TextEditingController _dirNumeroController =
        new TextEditingController(text: data['DireccionNumero'].toString());
    TextEditingController _numIntController =
        new TextEditingController(text: data['DireccionNumeroInt'].toString());
    TextEditingController _entreCallesController =
        new TextEditingController(text: data['EntreCalles'].toString());
    TextEditingController _delegacionController =
        new TextEditingController(text: data['Delegacion'].toString());
    TextEditingController _coloniaController =
        new TextEditingController(text: data['Colonia'].toString());
    TextEditingController _poblacionController =
        new TextEditingController(text: data['Poblacion'].toString());
    TextEditingController _codigoPostalController =
        new TextEditingController(text: data['CodigoPostal'].toString());

    FocusNode _contactoFocusNode = FocusNode();
    FocusNode _telefonoFocusNode = FocusNode();
    FocusNode _emailFocusNode = FocusNode();
    FocusNode _direccionFocusNode = FocusNode();
    FocusNode _dirNumeroFocusNode = FocusNode();
    FocusNode _numIntFocusNode = FocusNode();
    FocusNode _entreCallesFocusNode = FocusNode();
    FocusNode _delegacionFocusNode = FocusNode();
    FocusNode _coloniaFocusNode = FocusNode();
    FocusNode _poblacionFocusNode = FocusNode();
    FocusNode _codigoPostalFocusNode = FocusNode();

    try {
      DALayoutFormFilters _modelLayoutInfoCliente = new DALayoutFormFilters(
        heroTag: 'editarClienteScreen',
        refID: _refID,
        prefix: 'Editar',
        title: '${data['Nombre']}',
        hasBackButton: true,
        iconSubmit: Icons.save,
        formSubmit: (value) async {
          if (!telefonoValido(_telefonoController.text)) {
            Fluttertoast.showToast(
                msg: 'El teléfono no es valido',
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                timeInSecForIosWeb: 1,
                backgroundColor: Colors.black,
                textColor: Colors.white,
                fontSize: 16.0);
            _telefonoFocusNode.requestFocus();
          } else {
            if (!emailValido(_emailController.text)) {
              Fluttertoast.showToast(
                  msg: 'El email no es valido',
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.BOTTOM,
                  timeInSecForIosWeb: 1,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0);
              _emailFocusNode.requestFocus();
            } else {
              if (!codigoPostalValido(_codigoPostalController.text)) {
                Fluttertoast.showToast(
                    msg: 'El código postal no es valido',
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.BOTTOM,
                    timeInSecForIosWeb: 1,
                    backgroundColor: Colors.black,
                    textColor: Colors.white,
                    fontSize: 16.0);
                _codigoPostalFocusNode.requestFocus();
              } else {
                dynamic _resCart;
                try {
                  DARequestModel _req = ApiRequest.createCliente(
                    cliente: _data['Cliente'].toString(),
                    nombre: _data['Nombre'].toString(),
                    razonSocial: _data['RazonSocial'].toString(),
                    rfc: _data['RFC'].toString(),
                    contacto: _contactoController.text,
                    telefono: _telefonoController.text,
                    email: _emailController.text,
                    direccion: _direccionController.text,
                    dirNumero: _dirNumeroController.text,
                    numInterior: _numIntController.text,
                    entreCalles: _entreCallesController.text,
                    delegacion: _delegacionController.text,
                    colonia: _coloniaController.text,
                    poblacion: _poblacionController.text,
                    codigoPostal: _codigoPostalController.text,
                  ); // Petición prueba
                  _resCart = await DAMainLoadingProvider.updateAPI(
                    _req.uriReq,
                    _req.bodyReq,
                  );
                } catch (e) {
                  PrefsManager.setCambiosClientes(
                    cambio: json.encode(
                      {
                        'cliente': _data['Cliente'].toString(),
                        'nombre': _data['Nombre'].toString(),
                        'razonSocial': _data['RazonSocial'].toString(),
                        'rfc': _data['RFC'].toString(),
                        'contacto': _contactoController.text,
                        'telefono': _telefonoController.text,
                        'email': _emailController.text,
                        'direccion': _direccionController.text,
                        'dirNumero': _dirNumeroController.text,
                        'numInterior': _numIntController.text,
                        'entreCalles': _entreCallesController.text,
                        'delegacion': _delegacionController.text,
                        'colonia': _coloniaController.text,
                        'poblacion': _poblacionController.text,
                      },
                    ),
                  );
                  Fluttertoast.showToast(
                      msg:
                          'Error:$e\nEl cambio ha sido guardado para su posterior sincronización.',
                      toastLength: Toast.LENGTH_LONG,
                      gravity: ToastGravity.BOTTOM,
                      timeInSecForIosWeb: 3,
                      backgroundColor: Colors.black,
                      textColor: Colors.white,
                      fontSize: 16.0);
                }

                if (_resCart['OkRef'] == null) {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) {
                      return DAInputDialog(
                        title: 'Cliente Editado',
                        okText: 'Aceptar',
                        input: <Widget>[
                          Text(
                            'El cliente ${_resCart['Cliente']} fue editado correctamente.',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 24.0,
                            ),
                          ),
                        ],
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/ListPage',
                            arguments: AppDAClientes.index(
                              context,
                            ),
                          );
                        },
                        noCancel: true,
                      );
                    },
                  );
                } else {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) {
                      return DAInputDialog(
                        title: 'Error al editar cliente',
                        okText: 'Aceptar',
                        input: <Widget>[
                          Text(
                            'El cliente no pudo ser editado, error: ${_resCart['OkRef']}',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 24.0,
                            ),
                          ),
                        ],
                        onPressed: () {
                          Navigator.of(context)
                              .popUntil(ModalRoute.withName('home'));
                        },
                        noCancel: true,
                      );
                    },
                  );
                }
              }
            }
          }
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(
          children: [],
        ),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [
          DAInput(
            refID: 'contacto',
            controller: _contactoController,
            label: 'Contacto',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.textarea,
            focusNode: _contactoFocusNode,
            onFieldSubmitted: (value) {
              _contactoFocusNode.unfocus();
              _telefonoFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'telefono',
            controller: _telefonoController,
            label: 'Teléfono',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.number,
            focusNode: _telefonoFocusNode,
            onFieldSubmitted: (value) {
              _telefonoFocusNode.unfocus();
              _emailFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'email',
            controller: _emailController,
            label: 'Email',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.email,
            focusNode: _emailFocusNode,
            onFieldSubmitted: (value) {
              _emailFocusNode.unfocus();
              _direccionFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'direccion',
            controller: _direccionController,
            label: 'Dirección',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _direccionFocusNode,
            onFieldSubmitted: (value) {
              _direccionFocusNode.unfocus();
              _dirNumeroFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'dirNumero',
            controller: _dirNumeroController,
            label: 'Número exterior',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _dirNumeroFocusNode,
            onFieldSubmitted: (value) {
              _dirNumeroFocusNode.unfocus();
              _numIntFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'numInterior',
            controller: _numIntController,
            label: 'Número Interior',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _numIntFocusNode,
            onFieldSubmitted: (value) {
              _numIntFocusNode.unfocus();
              _entreCallesFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'entreCalles',
            controller: _entreCallesController,
            label: 'Entre Calles',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _entreCallesFocusNode,
            onFieldSubmitted: (value) {
              _entreCallesFocusNode.unfocus();
              _delegacionFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'delegacion',
            controller: _delegacionController,
            label: 'Delegación',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _delegacionFocusNode,
            onFieldSubmitted: (value) {
              _delegacionFocusNode.unfocus();
              _coloniaFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'colonia',
            controller: _coloniaController,
            label: 'Colonia',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _coloniaFocusNode,
            onFieldSubmitted: (value) {
              _coloniaFocusNode.unfocus();
              _poblacionFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'poblacion',
            controller: _poblacionController,
            label: 'Población',
            isRequired: false,
            disabled: false,
            tipo: DAInputType.string,
            focusNode: _poblacionFocusNode,
            onFieldSubmitted: (value) {
              _poblacionFocusNode.unfocus();
              _codigoPostalFocusNode.requestFocus();
            },
            textInputAction: TextInputAction.done,
          ),
          DAInput(
            refID: 'codigoPostal',
            controller: _codigoPostalController,
            label: 'Código Postal',
            isRequired: true,
            disabled: false,
            tipo: DAInputType.number,
            focusNode: _codigoPostalFocusNode,
            onFieldSubmitted: (value) {
              _codigoPostalFocusNode.unfocus();
            },
            textInputAction: TextInputAction.done,
          ),
        ];
        return _formBody;
      }

      // Descargamos Catálogos y asignamos valores default
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutInfoCliente;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }
}
