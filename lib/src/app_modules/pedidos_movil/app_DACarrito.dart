import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:no_context_navigation/no_context_navigation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

import '/src/DAPackagesRef.dart';
import '/src/app_config/api_request.dart';
import '/src/controllers/buscador_controller.dart';
import '/src/controllers/citas_controller.dart';
import '/src/controllers/cliente_controller.dart';
import '/src/controllers/dipath_controller.dart';
import '/src/controllers/lista_precios_controller.dart';
import '/src/controllers/pedidos_offline_controller.dart';
import '/src/controllers/sucursales_agente_controller.dart';
import '/src/controllers/agentes_sucursales_cte_controller.dart';
import '/src/controllers/ticket_controller.dart';
import '/src/controllers/user_config_controller.dart';
import '/src/helpers.dart';
import '/src/prefs_manager.dart';
import 'app_DAArticulos.dart';
import 'app_DAClientes.dart';

class AppDACarrito {
  /* Carrito */
  static index(BuildContext context, dynamic data) {
    String _id = data['ID'].toString();
    if (_id.isEmpty || _id == 'null') {
      _id = '${data['Cliente'].toString()}|${data['Nombre'].toString()}';
    }
    String _refID = 'detalleCliente|$_id';

    double _subtotal = 0;
    double _impuestos = 0;
    double _total = 0;
    double _articulosTotales = 0;

    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    BuscadorController _buscadorController = Get.find<BuscadorController>();

    TicketController _ticketController = Get.find<TicketController>();

    try {
      // ignore: avoid_init_to_null, unused_local_variable
      dynamic currentRow = null;
      List<dynamic> _dtData = [];

      totalize() async {
        _subtotal = 0;
        _impuestos = 0;
        _total = 0;
        _articulosTotales = 0;
        String _clienteActual = await PrefsManager.getClienteActual();
        String _zonaImpuesto = Get.find<DipathController>().getZonaImpuesto;

        // Iteramos sobre _dtData, que contiene los precios ajustados
        _dtData.forEach((articulo) {
          if (articulo['Cliente'] == _clienteActual &&
              articulo['RenglonTipo'] != 'C') {
            _subtotal += double.parse(articulo['Total']);
            double tasaImpuesto = _zonaImpuesto == 'FRONTERIZA' ? 0.08 : 0.16;
            _impuestos += double.parse(articulo['Total']) * tasaImpuesto;
            _articulosTotales += double.parse(articulo['Cantidad']);
          }
        });
        _total = _subtotal + _impuestos;
      }

      Future<FutureOr> onGoBack(Object? value) async {
        totalize();
        //addOptions();

        _formProv.pageRefresh();
      }

      String removeNullMoney(String? value) {
        if (value == null || value == 'null') return '0';
        return value;
      }

      confirmDialog() async {
        await totalize();
        String _clienteActual = await PrefsManager.getClienteActual();
        List<dynamic> _cart = [];

        TextEditingController _observacionesController =
            new TextEditingController();

        showDialog(
          context: context,
          builder: (context) {
            if (_articulosTotales > 0) {
              Fluttertoast.showToast(
                  msg: 'Pedido Actual Guardado',
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.SNACKBAR,
                  timeInSecForIosWeb: 1,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0);

              return DAInputDialog(
                title: '¿Está seguro de realizar el pedido?',
                input: [
                  DAInput(
                    refID: 'articulosTotales',
                    controller: new TextEditingController(
                        text: _articulosTotales.toString()),
                    label: 'Artículos Totales',
                    isRequired: false,
                    disabled: true,
                    tipo: DAInputType.string,
                  ),
                  DAInput(
                    refID: 'totalPedido',
                    controller: new TextEditingController(
                        text: '\$ ${_total.toString().toFormattedMoney()}'),
                    label: 'Total',
                    isRequired: false,
                    disabled: true,
                    tipo: DAInputType.string,
                  ),
                  DAInput(
                    refID: 'observaciones',
                    controller: _observacionesController,
                    label: 'Observaciones',
                    isRequired: false,
                    tipo: DAInputType.textarea,
                  )
                ],
                okText: 'Si',
                cancelText: 'No',
                onPressed: () async {
                  dynamic _resCart;
                  try {
                    EasyLoading.show(status: 'Procesando Pedido...');
                    String _cliente = await PrefsManager.getClienteActual();
                    EasyLoading.show(status: 'Obteniendo ubicación...');
                    Position _position =
                        await DAController.getCurrentLocation();
                    EasyLoading.show(status: 'Procesando Pedido...');

                    _ticketController.clearProductos();
                    _dtData.forEach((element) {
                      dynamic _articulo = element;
                      if (_articulo['Cliente'] == _clienteActual) {
                        _cart.add({
                          'Renglon': _articulo['Renglon'],
                          'Articulo': _articulo['Articulo'].toString(),
                          'SubCuenta': _articulo['SubCuenta'],
                          'Cantidad': _articulo['Cantidad'].toString(),
                          'Precio': _articulo['Precio'],
                          'Unidad': _articulo['Unidad'].toString(),
                          'DescuentoLinea': _articulo['DescuentoLinea'],
                          'Impuesto1': _articulo['Impuesto1'] ?? 0,
                          'Impuesto2': _articulo['Impuesto2'] ?? 0,
                          'Impuesto3': _articulo['Impuesto3'] ?? 0,
                          'Retencion1': _articulo['Retencion1'] ?? 0,
                          'Retencion2': _articulo['Retencion2'] ?? 0,
                          'Retencion3': _articulo['Retencion3'] ?? 0,
                          'RenglonTipo': _articulo['RenglonTipo'],
                          'DescripcionExtra':
                              _articulo['DescripcionExtra'] ?? '',
                        });

                        /* // Comenzamos a guardar el pedido de forma offline
                        Map<String, dynamic> _offlinePedido = {
                          'Detalle': _articulo,
                          'Cliente': _cliente,
                          'Observaciones': _observacionesController.text,
                          'FechaEmision': DateTime.now().toString(),
                          'Moneda': Get.find<UserConfigController>()
                              .usuarioMovil
                              .monedaBase,
                          'TipoCambio': Get.find<UserConfigController>()
                              .usuarioMovil
                              .tipoCambio,
                          'ListaPrecios':
                              Get.find<ListaPreciosController>().listaPrecios,
                          'CondicionPago':
                              Get.find<ClienteController>().getCondicionPago,
                          'Latitud': _position.latitude,
                          'Longitud': _position.longitude,
                        };

                        Get.find<PedidosOfflineController>()
                            .addPedidoOfflineFromJson(_offlinePedido); */

                        _ticketController.addProducto(Producto(
                          articulo: _articulo['Articulo'].toString(),
                          descripcion: _articulo['Descripcion'].toString(),
                          precio: double.parse(_articulo['Precio']),
                          impuesto1: double.parse(removeNullMoney(
                              _articulo['Impuesto1'].toString())),
                          impuesto2: double.parse(removeNullMoney(
                              _articulo['Impuesto2'].toString())),
                          impuesto3: double.parse(removeNullMoney(
                              _articulo['Impuesto3'].toString())),
                          retencion1: double.parse(removeNullMoney(
                              _articulo['Retencion1'].toString())),
                          retencion2: double.parse(removeNullMoney(
                              _articulo['Retencion2'].toString())),
                          retencion3: double.parse(removeNullMoney(
                              _articulo['Retencion3'].toString())),
                          cantidad: double.parse(_articulo['Cantidad']),
                          total: double.parse(_articulo['Precio']) *
                              double.parse(_articulo['Cantidad']),
                        ));
                      }
                    });

                    // Obtenemos el EnviarA basado en la sucursal del cliente seleccionada
                    String? enviarA;
                    final _sucursalesClienteController =
                        Get.find<AgentesSucursalesCteController>();
                    final sucursalSeleccionada = _sucursalesClienteController
                        .getSucursalClienteSeleccionada;
                    if (sucursalSeleccionada != null) {
                      enviarA = sucursalSeleccionada.id?.toString();
                    }

                    // Comenzamos a guardar el pedido de forma offline
                    Map<String, dynamic> _offlinePedido = {
                      'Detalle': _cart,
                      'Cliente': _cliente,
                      'Observaciones': _observacionesController.text,
                      'FechaEmision': DateTime.now().toString(),
                      'Moneda': Get.find<UserConfigController>().monedaUsuario,
                      'TipoCambio': Get.find<UserConfigController>()
                          .usuarioMovil
                          .tipoCambio,
                      'ListaPrecios':
                          Get.find<ListaPreciosController>().listaPrecios,
                      'CondicionPago':
                          Get.find<ClienteController>().getCondicionPago,
                      'EnviarA': enviarA,
                      'Latitud': _position.latitude,
                      'Longitud': _position.longitude,
                    };

                    Get.find<PedidosOfflineController>()
                        .addPedidoOfflineFromJson(_offlinePedido);

                    bool result =
                        await InternetConnectionChecker().hasConnection;
                    if (result) {
                      DARequestModel _reqCart = ApiRequest.pedido(
                          cliente: _cliente,
                          idCita: Get.find<CitasController>().getIdCita,
                          listaPrecios:
                              Get.find<ListaPreciosController>().listaPrecios,
                          condicionPago:
                              Get.find<ClienteController>().getCondicionPago,
                          lat: _position.latitude,
                          lng: _position.longitude,
                          carrito: _cart,
                          enviarA: enviarA,
                          observaciones: _observacionesController.text);
                      _resCart = await ApiRequest.execAPI(
                          _reqCart.uriReq, _reqCart.bodyReq,
                          update: true);
                      _ticketController.calcularTotal();
                      Get.find<CitasController>().reset();
                      EasyLoading.dismiss();
                    } else {
                      EasyLoading.dismiss();
                      Fluttertoast.showToast(
                          msg:
                              'No hay conexión a internet, el pedido se guardará de forma offline.',
                          toastLength: Toast.LENGTH_LONG,
                          gravity: ToastGravity.SNACKBAR,
                          timeInSecForIosWeb: 3,
                          backgroundColor: Colors.black,
                          textColor: Colors.white,
                          fontSize: 16.0);
                      Get.find<CitasController>().reset();
                      await PrefsManager.setCarritoVacio();
                    }
                  } catch (e) {
                    EasyLoading.dismiss();
                    Fluttertoast.showToast(
                        msg: 'Error: $e\nEl pedido ha sido guardado.',
                        toastLength: Toast.LENGTH_LONG,
                        gravity: ToastGravity.SNACKBAR,
                        timeInSecForIosWeb: 3,
                        backgroundColor: Colors.black,
                        textColor: Colors.white,
                        fontSize: 16.0);

                    navService.pushNamedAndRemoveUntil('home');
                    return;
                  }

                  // Validamos si hay internet, en caso de no haber, ejecutamos creacion de ticket automaticamente.
                  bool result = await InternetConnectionChecker().hasConnection;
                  if (!result || _resCart['Ok'] == null) {
                    _ticketController
                        .setMovimiento(result ? _resCart['Mov'] : 'Pedido');
                    _ticketController
                        .setMovimientoID(result ? _resCart['MovID'] : '');
                    if (!result) {
                      // Ejecución automática de la lógica normalmente en onCancelPressed

                      final pdf = pw.Document();

                      final netLogoImage = await imageFromAssetBundle(
                          'assets/app/logo-ticket.png');

                      // Función para dar formato de fecha con hora y minuto como se usa en México
                      String _formatDate(DateTime date) {
                        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
                      }

                      pw.Widget _header(pw.Context context) {
                        return pw.Container(
                          child: pw.Column(
                            children: [
                              // Se agrega row con logo y datos de la empresa
                              pw.Row(
                                children: [
                                  pw.Container(
                                    width: 200,
                                    height: 200,
                                    margin: pw.EdgeInsets.only(right: 25),
                                    child: pw.Image(netLogoImage),
                                  ),
                                  pw.Container(
                                    width: 300,
                                    child: pw.Column(
                                      children: [
                                        pw.Text(
                                          '${_ticketController.clienteNombre}',
                                          style: pw.TextStyle(
                                            fontSize: 20,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          '${_ticketController.clienteDireccion}',
                                          style: pw.TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        pw.Text(
                                          '${_ticketController.clienteTelefono}',
                                          style: pw.TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        pw.Text(
                                          '${_ticketController.clienteCorreo}',
                                          style: pw.TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              pw.Row(
                                children: [
                                  pw.Container(
                                    width: 200,
                                    height: 50,
                                    margin: pw.EdgeInsets.only(top: 25),
                                    padding: pw.EdgeInsets.all(5),
                                    decoration: pw.BoxDecoration(
                                      borderRadius: const pw.BorderRadius.all(
                                          pw.Radius.circular(2)),
                                      color: PdfColor.fromInt(0xFF0ABCDE),
                                    ),
                                    child: pw.GridView(
                                      crossAxisCount: 2,
                                      children: [
                                        pw.Text(
                                            '${_ticketController.movimiento}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text(
                                            '${_ticketController.movimientoID}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('Fecha:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text(_formatDate(DateTime.now()),
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                      ],
                                    ),
                                  ),
                                  pw.SizedBox(width: 50),
                                ],
                              ),
                              pw.SizedBox(height: 20),
                            ],
                          ),
                        );
                      }

                      pw.Widget _body(pw.Context context) {
                        const tableHeaders = [
                          'Artículo',
                          'Descripción',
                          'Precio',
                          'Cantidad',
                          'Total',
                        ];

                        return pw.TableHelper.fromTextArray(
                          border: null,
                          cellAlignment: pw.Alignment.centerLeft,
                          headerDecoration: pw.BoxDecoration(
                            borderRadius: const pw.BorderRadius.all(
                                pw.Radius.circular(2)),
                            color: PdfColor.fromInt(0xFF0ABCDE),
                          ),
                          headerHeight: 25,
                          cellHeight: 40,
                          cellAlignments: {
                            0: pw.Alignment.centerLeft,
                            1: pw.Alignment.centerLeft,
                            2: pw.Alignment.centerRight,
                            3: pw.Alignment.center,
                            4: pw.Alignment.centerRight,
                          },
                          headerStyle: pw.TextStyle(
                            color: PdfColors.white,
                            fontSize: 10,
                            fontWeight: pw.FontWeight.bold,
                          ),
                          cellStyle: const pw.TextStyle(
                            color: PdfColors.black,
                            fontSize: 10,
                          ),
                          rowDecoration: pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(
                                color: PdfColor.fromInt(0xFF00DDCB),
                                width: .5,
                              ),
                            ),
                          ),
                          headers: List<String>.generate(
                            tableHeaders.length,
                            (col) => tableHeaders[col],
                          ),
                          data: List<List<String>>.generate(
                            _ticketController.productos.length,
                            (row) => List<String>.generate(
                              tableHeaders.length,
                              (col) => _ticketController.productos[row]
                                  .getIndex(col),
                            ),
                          ),
                        );
                      }

                      pw.Widget _bodyFooter(pw.Context context) {
                        return pw.Container(
                          child: pw.Column(
                            children: [
                              pw.SizedBox(height: 20),
                              pw.Row(
                                children: [
                                  pw.Container(
                                    width: 200,
                                    height: 50,
                                    margin: pw.EdgeInsets.only(top: 25),
                                    padding: pw.EdgeInsets.all(5),
                                    decoration: pw.BoxDecoration(
                                      borderRadius: const pw.BorderRadius.all(
                                          pw.Radius.circular(2)),
                                      color: PdfColor.fromInt(0xFF0ABCDE),
                                    ),
                                    child: pw.GridView(
                                      crossAxisCount: 2,
                                      children: [
                                        pw.Text('Subtotal:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('${_ticketController.importe}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('IVA:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('${_ticketController.impuesto}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('Retención:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text(
                                            '${_ticketController.retencion}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('Total:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('${_ticketController.total}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                      ],
                                    ),
                                  ),
                                  pw.SizedBox(width: 50),
                                ],
                              ),
                              pw.SizedBox(height: 20),
                              pw.Text(
                                'Gracias por su compra',
                                style: pw.TextStyle(
                                  fontSize: 20,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      pw.Widget _footer(pw.Context context) {
                        return pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                          children: [
                            pw.Container(
                              height: 20,
                              width: 100,
                              child: pw.BarcodeWidget(
                                barcode: pw.Barcode.pdf417(),
                                data:
                                    '${_ticketController.movimiento} ${_ticketController.movimientoID}',
                                drawText: false,
                              ),
                            ),
                            pw.Text(
                              'Pagina ${context.pageNumber}/${context.pagesCount}',
                              style: const pw.TextStyle(
                                fontSize: 12,
                                color: PdfColors.black,
                              ),
                            ),
                          ],
                        );
                      }

                      pdf.addPage(
                        pw.MultiPage(
                          pageFormat: PdfPageFormat.a4,
                          header: _header,
                          footer: _footer,
                          build: (pw.Context context) => [
                            /* _header,
                      pw.SizedBox(height: 20), */
                            _body(context),
                            _bodyFooter(context),
                          ],
                        ),
                      );

                      await PrefsManager.setCarritoHistorial(
                        cart: _cart,
                        clienteActual: _clienteActual,
                      );

                      await PrefsManager.setCarritoVacio();

                      await Printing.sharePdf(
                          bytes: await pdf.save(),
                          filename:
                              '${_ticketController.movimiento} ${_ticketController.movimientoID}.pdf');
                      _ticketController.reset();

                      navService.pushNamedAndRemoveUntil('home');
                    } else {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return DAInputDialog(
                            title:
                                '${_resCart['Mov']} ${_resCart['MovID']} generado correctamente.',
                            subtitle:
                                'Si desea compartir el ticket, presione el botón de ticket.',
                            input: [],
                            okText: 'Aceptar',
                            cancelText: 'Ticket',
                            onPressed: () async {
                              Navigator.of(context).pop();
                              Navigator.of(context).pop();
                              await PrefsManager.setCarritoHistorial(
                                cart: _cart,
                                clienteActual: _clienteActual,
                              );
                              await PrefsManager.setCarritoVacio();
                              Get.find<PedidosOfflineController>().reset();
                              onGoBack(null);
                              navService.pushNamedAndRemoveUntil('home');
                            },
                            onCancelPressed: () async {
                              final pdf = pw.Document();

                              final netLogoImage = await imageFromAssetBundle(
                                  'assets/app/logo-ticket.png');

                              // Función para dar formato de fecha con hora y minuto como se usa en México
                              String _formatDate(DateTime date) {
                                return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
                              }

                              pw.Widget _header(pw.Context context) {
                                return pw.Container(
                                  child: pw.Column(
                                    children: [
                                      // Se agrega row con logo y datos de la empresa
                                      pw.Row(
                                        children: [
                                          pw.Container(
                                            width: 200,
                                            height: 200,
                                            margin:
                                                pw.EdgeInsets.only(right: 25),
                                            child: pw.Image(netLogoImage),
                                          ),
                                          pw.Container(
                                            width: 300,
                                            child: pw.Column(
                                              children: [
                                                pw.Text(
                                                  '${_ticketController.clienteNombre}',
                                                  style: pw.TextStyle(
                                                    fontSize: 20,
                                                    fontWeight:
                                                        pw.FontWeight.bold,
                                                  ),
                                                ),
                                                pw.Text(
                                                  '${_ticketController.clienteDireccion}',
                                                  style: pw.TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                                pw.Text(
                                                  '${_ticketController.clienteTelefono}',
                                                  style: pw.TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                                pw.Text(
                                                  '${_ticketController.clienteCorreo}',
                                                  style: pw.TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      pw.Row(
                                        children: [
                                          pw.Container(
                                            width: 200,
                                            height: 50,
                                            margin: pw.EdgeInsets.only(top: 25),
                                            padding: pw.EdgeInsets.all(5),
                                            decoration: pw.BoxDecoration(
                                              borderRadius:
                                                  const pw.BorderRadius.all(
                                                      pw.Radius.circular(2)),
                                              color:
                                                  PdfColor.fromInt(0xFF0ABCDE),
                                            ),
                                            child: pw.GridView(
                                              crossAxisCount: 2,
                                              children: [
                                                pw.Text(
                                                    '${_ticketController.movimiento}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.movimientoID}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('Fecha:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    _formatDate(DateTime.now()),
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                              ],
                                            ),
                                          ),
                                          pw.SizedBox(width: 50),
                                        ],
                                      ),
                                      pw.SizedBox(height: 20),
                                    ],
                                  ),
                                );
                              }

                              pw.Widget _body(pw.Context context) {
                                const tableHeaders = [
                                  'Artículo',
                                  'Descripción',
                                  'Precio',
                                  'Cantidad',
                                  'Total',
                                ];

                                return pw.TableHelper.fromTextArray(
                                  border: null,
                                  cellAlignment: pw.Alignment.centerLeft,
                                  headerDecoration: pw.BoxDecoration(
                                    borderRadius: const pw.BorderRadius.all(
                                        pw.Radius.circular(2)),
                                    color: PdfColor.fromInt(0xFF0ABCDE),
                                  ),
                                  headerHeight: 25,
                                  cellHeight: 40,
                                  cellAlignments: {
                                    0: pw.Alignment.centerLeft,
                                    1: pw.Alignment.centerLeft,
                                    2: pw.Alignment.centerRight,
                                    3: pw.Alignment.center,
                                    4: pw.Alignment.centerRight,
                                  },
                                  headerStyle: pw.TextStyle(
                                    color: PdfColors.white,
                                    fontSize: 10,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                  cellStyle: const pw.TextStyle(
                                    color: PdfColors.black,
                                    fontSize: 10,
                                  ),
                                  rowDecoration: pw.BoxDecoration(
                                    border: pw.Border(
                                      bottom: pw.BorderSide(
                                        color: PdfColor.fromInt(0xFF00DDCB),
                                        width: .5,
                                      ),
                                    ),
                                  ),
                                  headers: List<String>.generate(
                                    tableHeaders.length,
                                    (col) => tableHeaders[col],
                                  ),
                                  data: List<List<String>>.generate(
                                    _ticketController.productos.length,
                                    (row) => List<String>.generate(
                                      tableHeaders.length,
                                      (col) => _ticketController.productos[row]
                                          .getIndex(col),
                                    ),
                                  ),
                                );
                              }

                              pw.Widget _bodyFooter(pw.Context context) {
                                return pw.Container(
                                  child: pw.Column(
                                    children: [
                                      pw.SizedBox(height: 20),
                                      pw.Row(
                                        children: [
                                          pw.Container(
                                            width: 200,
                                            height: 50,
                                            margin: pw.EdgeInsets.only(top: 25),
                                            padding: pw.EdgeInsets.all(5),
                                            decoration: pw.BoxDecoration(
                                              borderRadius:
                                                  const pw.BorderRadius.all(
                                                      pw.Radius.circular(2)),
                                              color:
                                                  PdfColor.fromInt(0xFF0ABCDE),
                                            ),
                                            child: pw.GridView(
                                              crossAxisCount: 2,
                                              children: [
                                                pw.Text('Subtotal:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalSubtotal}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('IVA:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalIva}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('Retención:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalRetencion}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('Total:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalPrice}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                              ],
                                            ),
                                          ),
                                          pw.SizedBox(width: 50),
                                        ],
                                      ),
                                      pw.SizedBox(height: 20),
                                      pw.Text(
                                        'Gracias por su compra',
                                        style: pw.TextStyle(
                                          fontSize: 20,
                                          fontWeight: pw.FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }

                              pw.Widget _footer(pw.Context context) {
                                return pw.Row(
                                  mainAxisAlignment:
                                      pw.MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                                  children: [
                                    pw.Container(
                                      height: 20,
                                      width: 100,
                                      child: pw.BarcodeWidget(
                                        barcode: pw.Barcode.pdf417(),
                                        data:
                                            '${_ticketController.movimiento} ${_ticketController.movimientoID}',
                                        drawText: false,
                                      ),
                                    ),
                                    pw.Text(
                                      'Pagina ${context.pageNumber}/${context.pagesCount}',
                                      style: const pw.TextStyle(
                                        fontSize: 12,
                                        color: PdfColors.black,
                                      ),
                                    ),
                                  ],
                                );
                              }

                              pdf.addPage(
                                pw.MultiPage(
                                  pageFormat: PdfPageFormat.a4,
                                  header: _header,
                                  footer: _footer,
                                  build: (pw.Context context) => [
                                    /* _header,
                      pw.SizedBox(height: 20), */
                                    _body(context),
                                    _bodyFooter(context),
                                  ],
                                ),
                              );

                              await PrefsManager.setCarritoHistorial(
                                cart: _cart,
                                clienteActual: _clienteActual,
                              );

                              await PrefsManager.setCarritoVacio();

                              await Printing.sharePdf(
                                  bytes: await pdf.save(),
                                  filename:
                                      '${_ticketController.movimiento} ${_ticketController.movimientoID}.pdf');
                              _ticketController.reset();
                              Get.find<PedidosOfflineController>().reset();

                              navService.pushNamedAndRemoveUntil('home');
                            },
                          );
                        },
                      );
                    }
                  } else {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return DAInputDialog(
                          title: 'Error al generar el pedido',
                          input: [],
                          okText: 'Aceptar',
                          cancelText: '',
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                        );
                      },
                    );
                  }
                },
              );
            } else {
              return DAInputDialog(
                title:
                    'No hay artículos en el carrito para realizar el pedido.',
                input: [],
                okText: 'Aceptar',
                cancelText: '',
                onPressed: () async {
                  Navigator.of(context).pop();
                },
              );
            }
          },
        );
      }

      _finderAdd() async {
        String tempArticles = await PrefsManager.getArticulos();
        List<dynamic> articulos = json.decode(tempArticles);

        // Ordenar artículos por existencia (mayor a menor)
        articulos.sort((a, b) {
          double existenciaA =
              double.tryParse(a['Existencia']?.toString() ?? '0') ?? 0;
          double existenciaB =
              double.tryParse(b['Existencia']?.toString() ?? '0') ?? 0;
          return existenciaB.compareTo(existenciaA);
        });

        final articulosOriginal = List.unmodifiable(articulos);
        PrefsManager.clearLastFinderFilteredData();
        PrefsManager.clearFinderFilters();

        _buscadorController.articulos.forEach((articuloAgregado) {
          articulos.forEach((articulo) {
            if (articulo['Articulo'] == articuloAgregado.articulo) {
              articulo['checked'] = true;
            }
          });
        });

        showSearch(
          context: context,
          delegate: DASearchDelegateOfflineMultiple(
            closeOnTap: false,
            minLengthQuery: 3,
            dataSource: articulos,
            dataSourceSelected: _buscadorController.articulosJson,
            dataOriginal: articulosOriginal,
            configTiles: DAFinderCardModel(
              title: "Descripcion1",
              leading: "Articulo",
              trailing: "Codigo",
              icon: "icon",
              moreLabels: ["Existencia", "Unidad"],
              filterLabels: [
                "Categoria",
                "Grupo",
                "Familia",
                "Linea",
                "Tipo",
                "TipoOpcion"
              ],
            ),
            searchLabel: "Buscar Artículos...",
            noDataLabel: "Artículos",
            scannerFinder: true,
            onFinderTap: (value) async {
              dynamic args = value;
              _buscadorController.prepararJuego(args);

              _buscadorController.addArticulo(Articulo.fromJson(args));

              articulos.forEach((articulo) {
                articulo['checked'] = false;
              });

              _buscadorController.articulos.forEach((articuloAgregado) {
                articulos.forEach((articulo) {
                  if (articulo['Articulo'] == articuloAgregado.articulo) {
                    articulo['checked'] = true;
                  }
                });
              });

              Fluttertoast.cancel();

              Fluttertoast.showToast(
                  msg: _buscadorController.mostrarMensaje(),
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.SNACKBAR,
                  timeInSecForIosWeb: 3,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0);

              /* Navigator.pushNamed(
                context,
                '/WidgetsForm',
                arguments: AppDAArticulos.informacion(context, args),
              ).then(onGoBack); */
            },
            onDoneTap: (p0) {
              Navigator.of(context).pop();
              Navigator.pushNamed(
                context,
                '/WidgetsForm',
                arguments: AppDAArticulos.informacionMultiple(context),
              ).then(onGoBack);
            },
          ),
        );
      }

      DALayoutFormFilters _modelLayoutCarrito = new DALayoutFormFilters(
        refID: _refID,
        prefix: 'Carrito de Compras',
        title: data['Cliente'],
        hasBackButton: true,
        iconSubmit: Icons.send,
        formSubmit: (value) async {
          await confirmDialog();
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(children: [
          DABottomAppBarButton(
            label: 'Vaciar',
            icon: Icons.delete,
            onTap: () async {
              List<String>? tempCart = await PrefsManager.getCarrito();
              String _clienteActual = await PrefsManager.getClienteActual();
              bool _isEmpty = true;

              if (tempCart != null && tempCart.isNotEmpty) {
                tempCart.forEach(
                  (element) {
                    dynamic articulo = json.decode(element);
                    if (articulo['Cliente'] == _clienteActual) {
                      _isEmpty = false;
                    }
                  },
                );
              }

              if (_isEmpty) {
                showDialog(
                  context: context,
                  builder: (context) {
                    return DAInputDialog(
                      title: 'No hay artículos en el carrito.',
                      input: [],
                      okText: 'Aceptar',
                      cancelText: '',
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    );
                  },
                );
              } else {
                showDialog(
                  context: context,
                  builder: (context) {
                    return DAInputDialog(
                      title: '¿Está seguro de vaciar el carrito?',
                      input: [],
                      okText: 'Si',
                      cancelText: 'No',
                      onPressed: () async {
                        Navigator.of(context).pop();
                        await PrefsManager.setCarritoVacio();
                        onGoBack(null);
                      },
                      onCancelPressed: () {
                        Navigator.of(context).pop();
                      },
                    );
                  },
                );
              }
            },
          ),
          DABottomAppBarButton(
            label: 'Agregar',
            icon: Icons.add,
            onTap: () {
              _finderAdd();
              onGoBack(null);
            },
          ),
          DABottomAppBarButton(
            label: '',
            onTap: null,
          ),
        ]),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        DADataTablePro _dtWidget = DADataTablePro(
          refID: 'dtXAfectar',
          scopeRowID: 'ID',
          noDataMsg: 'artículos',
          hasFinder: true,
          columnsDef: <DAConfigRowModel>[
            DAConfigRowModel(
              label: "Artículo",
              scope: "Articulo",
              isnumeric: false,
            ),
            DAConfigRowModel(
              label: "Total",
              scope: "TotalVisual",
            ),
            DAConfigRowModel(
              label: "Cantidad",
              scope: "CantidadVisual",
            ),
            DAConfigRowModel(
              label: "Unidad",
              scope: "Unidad",
            ),
            DAConfigRowModel(
              label: "Precio Unitario",
              scope: "Precio",
            ),
            DAConfigRowModel(
              label: "Tipo",
              scope: "Tipo",
            ),
            DAConfigRowModel(
              label: "Descripción",
              scope: "Descripcion",
            ),
            DAConfigRowModel(
              label: "Observaciones",
              scope: "DescripcionExtra",
            ),
          ],
          dataSource: _dtData,
          tableActions: DALayoutDetModelTableActionsModel(
            onTap: (value) {
              currentRow = value;
            },
            onDoubleTap: (value) {
              currentRow = value;
            },
            onLongPress: (value) {
              currentRow = value;
            },
          ),
          listLongPress: [
            /* DADataTableListTile(
              icon: Icons.settings,
              title: 'Opciones de Artículo',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                if (_opcionesController
                    .articuloCoincide(currentRow['Articulo'].toString())) {
                  _opcionesController.articuloActual.reset();
                  Navigator.pushNamed(
                    context,
                    '/WidgetsForm',
                    arguments: AppDAArticulos.opciones(context, currentRow),
                  ).then(onGoBack);
                } else {
                  DAToast(context, 'Artículo sin opciones.');
                }
              },
            ), */
            DADataTableListTile(
              icon: Icons.info,
              title: 'Ver Juego',
              validation: (value) {
                return value['Tipo'] == 'Juego';
              },
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments: AppDAArticulos.juego(
                      context, articulo, articulo['Cantidad'].toString()),
                ).then(onGoBack);
              },
            ),
            DADataTableListTile(
              icon: Icons.info,
              title: 'Ver Detalle',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments:
                      AppDAArticulos.informacionNoEdit(context, articulo),
                ).then(onGoBack);
              },
            ),
            DADataTableListTile(
              icon: Icons.edit,
              title: 'Modificar',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                showDialog(
                  context: context,
                  builder: (context) {
                    TextEditingController _cantidadController =
                        TextEditingController();
                    return DAInputDialog(
                      title: 'Ingrese la nueva cantidad',
                      input: [
                        DAInput(
                          refID: 'nuevaCantidad',
                          controller: _cantidadController,
                          label: 'Nueva Cantidad',
                          isRequired: false,
                          disabled: false,
                          tipo: DAInputType.numberThreeDecimals,
                        ),
                      ],
                      okText: 'Confirmar',
                      cancelText: 'Cancelar',
                      onPressed: () async {
                        if (_cantidadController.text.isNotEmpty) {
                          Navigator.of(context).pop();
                          articulo['Cantidad'] = _cantidadController.text;
                          articulo['CantidadVisual'] =
                              _cantidadController.text.toFormattedNumber();
                          articulo['Total'] =
                              (double.parse(_cantidadController.text) *
                                      double.parse(articulo['Precio']))
                                  .toStringAsFixed(2);
                          articulo['TotalVisual'] = '\$ ${articulo['Total']}';

                          await PrefsManager.setCarritoModificar(
                              articulo: json.encode(articulo));
                          onGoBack(null);
                        }
                      },
                      onCancelPressed: () {
                        Navigator.of(context).pop();
                      },
                    );
                  },
                );
              },
            ),
            DADataTableListTile(
              icon: Icons.edit_note,
              title: 'Modificar Observaciones',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                showDialog(
                  context: context,
                  builder: (context) {
                    TextEditingController _observacionesController =
                        TextEditingController(
                            text: articulo['DescripcionExtra'] ?? '');
                    return DAInputDialog(
                      title: 'Ingrese las observaciones',
                      input: [
                        DAInput(
                          refID: 'observaciones',
                          controller: _observacionesController,
                          label: 'Observaciones',
                          isRequired: false,
                          disabled: false,
                          tipo: DAInputType.textarea,
                        ),
                      ],
                      okText: 'Confirmar',
                      cancelText: 'Cancelar',
                      onPressed: () async {
                        Navigator.of(context).pop();
                        articulo['DescripcionExtra'] =
                            _observacionesController.text;

                        await PrefsManager.setCarritoModificar(
                            articulo: json.encode(articulo));
                        onGoBack(null);
                      },
                      onCancelPressed: () {
                        Navigator.of(context).pop();
                      },
                    );
                  },
                );
              },
            ),
            DADataTableListTile(
              icon: Icons.delete,
              title: 'Eliminar',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                await PrefsManager.setCarritoEliminar(
                    articulo: json.encode(articulo));
                onGoBack(null);
              },
            ),
          ],
        );

        List<Widget> _formBody = [
          /* TextButton(
              onPressed: () async {
                final pdf = pw.Document();

                final netLogoImage = await networkImage(
                    'https://cdn2.unrealengine.com/Diesel%2Fproduct%2Ftctd2%2Flogos%2Fgame_logo_color_1000x375-1000x375-32062fa9b2223a398be7abb362c7166d1a7d7a44.png?h=270&resize=1&w=480');

                // Función para dar formato de fecha con hora y minuto como se usa en México
                String _formatDate(DateTime date) {
                  return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
                }

                pw.Widget _header(pw.Context context) {
                  return pw.Container(
                    child: pw.Column(
                      children: [
                        // Se agrega row con logo y datos de la empresa
                        pw.Row(
                          children: [
                            pw.Container(
                              width: 200,
                              height: 200,
                              margin: pw.EdgeInsets.only(right: 25),
                              child: pw.Image(netLogoImage),
                            ),
                            pw.Container(
                              width: 300,
                              child: pw.Column(
                                children: [
                                  /* pw.Text(
                                  'Pedido',
                                  style: pw.TextStyle(
                                    fontSize: 20,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                                pw.Text(
                                  'Fecha: ${DateTime.now().toString()}',
                                  style: pw.TextStyle(
                                    fontSize: 12,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ), */
                                  pw.Text(
                                    'Muebles y Accesorios',
                                    style: pw.TextStyle(
                                      fontSize: 20,
                                      fontWeight: pw.FontWeight.bold,
                                    ),
                                  ),
                                  pw.Text(
                                    'Calle 1 # 2 - 3',
                                    style: pw.TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  pw.Text(
                                    'Teléfono: 1234567890',
                                    style: pw.TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  pw.Text(
                                    '<EMAIL>',
                                    style: pw.TextStyle(
                                      fontSize: 20,
                                      fontWeight: pw.FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        pw.Row(
                          children: [
                            pw.Container(
                              width: 200,
                              height: 50,
                              margin: pw.EdgeInsets.only(top: 25),
                              padding: pw.EdgeInsets.all(5),
                              decoration: pw.BoxDecoration(
                                borderRadius: const pw.BorderRadius.all(
                                    pw.Radius.circular(2)),
                                color: PdfColor.fromInt(0xFF0ABCDE),
                              ),
                              child: pw.GridView(
                                crossAxisCount: 2,
                                children: [
                                  pw.Text('Pedido #',
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                  pw.Text('999',
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                  pw.Text('Fecha:',
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                  pw.Text(_formatDate(DateTime.now()),
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                ],
                              ),
                            ),
                            pw.SizedBox(width: 50),
                          ],
                        ),
                        pw.SizedBox(height: 20),
                      ],
                    ),
                  );
                }

                pw.Widget _body(pw.Context context) {
                  const tableHeaders = [
                    'Artículo',
                    'Descripción',
                    'Precio',
                    'Cantidad',
                    'Total',
                  ];

                  return pw.Table.fromTextArray(
                    border: null,
                    cellAlignment: pw.Alignment.centerLeft,
                    headerDecoration: pw.BoxDecoration(
                      borderRadius:
                          const pw.BorderRadius.all(pw.Radius.circular(2)),
                      color: PdfColor.fromInt(0xFF0ABCDE),
                    ),
                    headerHeight: 25,
                    cellHeight: 40,
                    cellAlignments: {
                      0: pw.Alignment.centerLeft,
                      1: pw.Alignment.centerLeft,
                      2: pw.Alignment.centerRight,
                      3: pw.Alignment.center,
                      4: pw.Alignment.centerRight,
                    },
                    headerStyle: pw.TextStyle(
                      color: PdfColors.white,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    cellStyle: const pw.TextStyle(
                      color: PdfColors.black,
                      fontSize: 10,
                    ),
                    rowDecoration: pw.BoxDecoration(
                      border: pw.Border(
                        bottom: pw.BorderSide(
                          color: PdfColor.fromInt(0xFF00DDCB),
                          width: .5,
                        ),
                        ),
                      ),
                    headers: List<String>.generate(
                      tableHeaders.length,
                      (col) => tableHeaders[col],
                    ),
                    data: List<List<String>>.generate(
                      _ticketController.products.length,
                      (row) => List<String>.generate(
                        tableHeaders.length,
                        (col) => _ticketController.products[row].getIndex(col),
                      ),
                    ),
                  );
                }

                pw.Widget _footer(pw.Context context) {
                  return pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Container(
                        height: 20,
                        width: 100,
                        child: pw.BarcodeWidget(
                          barcode: pw.Barcode.pdf417(),
                          data: 'Pedido# 999',
                          drawText: false,
                        ),
                      ),
                      pw.Text(
                        'Pagina ${context.pageNumber}/${context.pagesCount}',
                        style: const pw.TextStyle(
                          fontSize: 12,
                          color: PdfColors.black,
                        ),
                      ),
                    ],
                  );
                }

                pdf.addPage(
                  pw.MultiPage(
                    pageFormat: PdfPageFormat.a4,
                    header: _header,
                    footer: _footer,
                    build: (pw.Context context) => [
                      /* _header,
                      pw.SizedBox(height: 20), */
                      _body(context),
                    ],
                  ),
                );

                await Printing.sharePdf(
                    bytes: await pdf.save(), filename: 'my-document.pdf');
              },
              child: Text('Ticket')), */
          TextButton.icon(
              style: ButtonStyle(
                alignment: Alignment.centerLeft,
                padding: WidgetStateProperty.all(
                  EdgeInsets.only(left: 16.0),
                ),
              ),
              onPressed: () async {
                String _clienteActual = await PrefsManager.getClienteActual();
                String tempClientes = await PrefsManager.getClientes();
                dynamic tempJsonClientes = json.decode(tempClientes);
                List<dynamic> clientes = tempJsonClientes['Clientes'];
                dynamic infoCLiente = clientes.firstWhere(
                  (element) {
                    return element['Cliente'] == _clienteActual;
                  },
                );

                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments: AppDAClientes.informacionFromCarrito(
                    context,
                    infoCLiente,
                  ),
                ).then(onGoBack);
              },
              icon: Icon(Icons.info),
              label: Text('Información Cliente')),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Text(
              'Artículos: ${_articulosTotales.toString().toFormattedNumber()}',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24.0),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Text(
              'Subtotal: \$ ${_subtotal.toString().toFormattedMoney()}',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24.0),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Text(
              'Impuestos: \$ ${_impuestos.toString().toFormattedMoney()}',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24.0),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Text(
              'Total: \$ ${_total.toString().toFormattedMoney()}',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24.0),
            ),
          ),
          _dtWidget,
        ];

        return _formBody;
      }

      // Descargamos datos para tabla
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        List<String>? tempCart = await PrefsManager.getCarrito();
        String _clienteActual = await PrefsManager.getClienteActual();
        List<dynamic> newData = [];

        EasyLoading.show(status: 'Procesando...');
        if (tempCart != null && tempCart.isNotEmpty) {
          for (String element in tempCart) {
            dynamic articulo = json.decode(element);
            if (articulo['Cliente'] == _clienteActual) {
              DARequestModel _req = ApiRequest.ajusteDipath(
                cliente: Get.find<DipathController>().getCliente,
                articulo: articulo['Articulo'],
                cantidad: num.parse(articulo['Cantidad']),
                precioInicial: num.parse(articulo['PrecioOriginal']),
                sucursalAgente:
                    Get.find<SucursalesAgenteController>().sucursalSeleccionada,
              );

              dynamic data =
                  await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
              articulo['Precio'] = data[0]['PrecioFinal'].toString();
              double precio = double.parse(articulo['Precio']);
              double cantidad = double.parse(articulo['Cantidad']);
              double subtotalArticulo = precio * cantidad;
              String _zonaImpuesto =
                  Get.find<DipathController>().getZonaImpuesto;
              double tasaImpuesto = _zonaImpuesto == 'FRONTERIZA' ? 0.08 : 0.16;
              double totalArticulo =
                  subtotalArticulo + (subtotalArticulo * tasaImpuesto);
              articulo['Total'] = subtotalArticulo.toString();
              articulo['TotalVisual'] =
                  '\$ ${totalArticulo.toString().toFormattedMoney()}';

              newData.add(articulo);
            }
          }
        }

        _dtData.clear();
        _dtData.addAll(newData);

        await totalize();

        EasyLoading.dismiss();

        _formBodyRes = await createWidgets();

        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();

      _modelLayoutCarrito.onRefresh = downloadMedia;
      return _modelLayoutCarrito;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }
}
