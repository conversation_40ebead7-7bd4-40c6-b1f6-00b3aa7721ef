import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

import '/src/DAPackagesRef.dart';
import '/src/app_config/api_request.dart';
import '/src/controllers/citas_controller.dart';
import '/src/controllers/cliente_controller.dart';
import '/src/controllers/lista_precios_controller.dart';
import '/src/prefs_manager.dart';
import 'app_DACarrito.dart';
import 'app_DAClientes.dart';

class AppDAAgenda {
  /* Lista de Citas */
  static index(BuildContext context) {
    String _refID = 'agenda';
    DALayoutListCardProvider _layoutListProv =
        DALayoutListCardQueueProv.getByRefID(_refID);

    ListaPreciosController _listaPreciosController =
        Get.find<ListaPreciosController>();

    FutureOr onGoBack(Object? value) {
      Navigator.of(context).pop();
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();

      Fluttertoast.showToast(
          msg: 'Pedido Actual Guardado',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          fontSize: 16.0);
    }

    DALayoutListModel modelLayout = new DALayoutListModel(
      refID: _refID,
      prefix: 'Agenda de',
      title: 'Citas',
      hintText: 'Buscar...',
      noDataMsg: 'citas',
      hasBackButton: false,
      cardConfig: DADefListModel(
        id: "ID",
        title: "Cliente",
        subtitle: "Direccion",
        leading: "Situacion",
        trailing: "FechaD",
        icon: "agenda",
        onCardTap: (value) async {
          dynamic args = json.decode(value.metadata);
          await PrefsManager.setClienteActual(
              clienteActual: args['Contacto'].toString());
          showDialog(
            context: context,
            builder: (context) {
              dynamic cliente = json.decode(value.metadata);
              Get.find<CitasController>().setIdCita = cliente['IDCita'];
              return DAInputDialog(
                title: '${cliente['Cliente']}',
                subtitle: 'Seleccione una acción para ${cliente['Cliente']}',
                okText: 'Avanzar',
                cancelText: 'Cancelar',
                onPressed: () async {
                  // Si estado es Con Pedido o Cancelado, mostrar mensaje de que no es posible avanzar
                  if (cliente['Situacion'].toString().toLowerCase() ==
                          'con pedido' ||
                      cliente['Situacion'].toString().toLowerCase() ==
                          'cancelado') {
                    Navigator.of(context).pop();
                    Fluttertoast.showToast(
                        msg:
                            'No es posible avanzar con el estado ${cliente['Situacion']}',
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.black,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  } else {
                    // Verificamos si tiene lista de precios
                    _listaPreciosController.resetListaPrecios();
                    if (args['ListaPreciosEsp'] != null &&
                        args['ListaPreciosEsp'].toString().isNotEmpty) {
                      if (args['ListaPreciosEsp'] != '(Precio Lista)') {
                        _listaPreciosController
                            .setListaPrecios(args['ListaPreciosEsp']);
                      }
                    }

                    // Agregamos condicion de pago del cliente
                    Get.find<ClienteController>().reset();
                    Get.find<ClienteController>().setCondicionPago =
                        args['Condicion'];
                    Navigator.pushNamed(
                      context,
                      '/WidgetsForm',
                      arguments: AppDACarrito.index(
                        context,
                        args,
                      ),
                    ).then(onGoBack);
                  }
                },
                onCancelPressed: () async {
                  // Si estado es Con Pedido o Cancelado, mostrar mensaje de que no es posible cancelar
                  if (cliente['Situacion'].toString().toLowerCase() ==
                          'con pedido' ||
                      cliente['Situacion'].toString().toLowerCase() ==
                          'cancelado') {
                    Fluttertoast.showToast(
                        msg:
                            'Cita ya se encuentra en estado ${cliente['Situacion']}',
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.black,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  } else {
                    Navigator.of(context).pop();
                    dynamic tempCatalogos = await PrefsManager.getCatalogos();
                    dynamic tempJsonCatalogos = json.decode(tempCatalogos);
                    List<dynamic> subSituaciones =
                        tempJsonCatalogos['SubSituacion'];

                    List<dynamic> subSituacionesAgenda = [];
                    List<String> campanasValidas = [
                      'PedidosMovil',
                      'Pedidos Movil',
                      'Pedidos Móvil',
                      'PedidoMovil',
                      'Pedido Movil',
                      'Pedido Móvil'
                    ];
                    subSituaciones.forEach((element) {
                      if (campanasValidas.contains(element['CampanaTipo'])) {
                        subSituacionesAgenda.add(element);
                      }
                    });

                    List<TextEditingController> cancelacionAgendaControllers =
                        [];
                    subSituacionesAgenda.forEach((element) {
                      if (element['Situacion'].toString().toLowerCase() ==
                          'cancelado') {
                        cancelacionAgendaControllers.add(
                            new TextEditingController(
                                text: element['SubSituacion'].toString()));
                      }
                    });

                    List<dynamic> tempCancelaciones = [];

                    bool noHayCancelaciones = false;

                    if (cancelacionAgendaControllers.isNotEmpty) {
                      // Remover duplicados
                      Set<String> cancelaciones = new Set<String>();
                      cancelacionAgendaControllers.forEach((element) {
                        if (element.text.isNotEmpty) {
                          cancelaciones.add(element.text);
                        }
                      });
                      // Agregamos motivos de cancelacion
                      cancelaciones.forEach((element) {
                        tempCancelaciones.add({
                          'Valor': element,
                          'Situacion': element,
                        });
                      });
                    } else {
                      noHayCancelaciones = true;
                      Fluttertoast.showToast(
                          msg: 'No hay motivos de cancelación',
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.BOTTOM,
                          timeInSecForIosWeb: 1,
                          backgroundColor: Colors.black,
                          textColor: Colors.white,
                          fontSize: 16.0);
                    }

                    String motivoCancelacionController = '';

                    List<Widget> cancelacionAgendaInputs = [
                      DARadioList(
                        data: tempCancelaciones,
                        controllerValue: motivoCancelacionController,
                        value: 'Valor',
                        text: 'Situacion',
                        onChanged: (value) {
                          motivoCancelacionController = value.toString();
                          Fluttertoast.showToast(
                              msg: value.toString(),
                              toastLength: Toast.LENGTH_SHORT,
                              gravity: ToastGravity.BOTTOM,
                              timeInSecForIosWeb: 1,
                              backgroundColor: Colors.black,
                              textColor: Colors.white,
                              fontSize: 16.0);
                        },
                      )
                    ];

                    showDialog(
                        context: context,
                        builder: (context) {
                          return DAInputDialog(
                            title: 'Seleccione motivo de cancelación',
                            input: cancelacionAgendaInputs,
                            okText: 'Confirmar',
                            cancelText: 'Re Agendar',
                            onPressed: () async {
                              if (!noHayCancelaciones) {
                                Position currPosition =
                                    await DAController.getCurrentLocation();
                                String id = args['IDCita'].toString();

                                DARequestModel _req = ApiRequest.cancelCita(
                                  id: id,
                                  lat: currPosition.latitude,
                                  lng: currPosition.longitude,
                                  motivo: motivoCancelacionController,
                                );
                                await ApiRequest.execAPI(
                                    _req.uriReq, _req.bodyReq,
                                    update: true);
                              }

                              Navigator.of(context).pop();
                            },
                            onCancelPressed: () async {
                              EasyLoading.show(
                                  status: 'Obteniendo ubicación...');
                              Position currPosition =
                                  await DAController.getCurrentLocation();
                              String id = args['IDCita'].toString();
                              EasyLoading.dismiss();

                              EasyLoading.show(status: 'Reagendando cita...');
                              DARequestModel _req = ApiRequest.rescheduleCita(
                                  id: id,
                                  lat: currPosition.latitude,
                                  lng: currPosition.longitude);
                              dynamic _resReschedule = await ApiRequest.execAPI(
                                  _req.uriReq, _req.bodyReq,
                                  update: true);
                              EasyLoading.dismiss();

                              Fluttertoast.showToast(
                                  msg:
                                      'Se reagendó la cita ${_resReschedule['IdVisita']} con éxito',
                                  toastLength: Toast.LENGTH_SHORT,
                                  gravity: ToastGravity.BOTTOM,
                                  timeInSecForIosWeb: 1,
                                  backgroundColor: Colors.black,
                                  textColor: Colors.white,
                                  fontSize: 16.0);

                              Navigator.of(context).pop();
                            },
                          );
                        });
                  }
                },
                input: [],
              );
            },
          );
        },
      ),
      bottomNavigationBar: DABottomAppBar(
        children: [
          TextButton.icon(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: () {
              Navigator.pushNamed(
                context,
                '/ListPage',
                arguments: AppDAClientes.index(
                  context,
                ),
              ).then(onGoBack);
            },
            icon: Icon(
              Icons.people,
              size: 18,
              color: Colors.black.withValues(alpha: 0.25),
            ),
            label: Text(
              'Clientes',
              style: TextStyle(color: Colors.black.withValues(alpha: 0.25)),
            ),
          ),
          TextButton.icon(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: () {},
            icon: Icon(Icons.calendar_month),
            label: Text(
              'Agenda',
              style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold),
            ),
          ),
          DABottomAppBarButton(
            label: '                    ',
            onTap: null,
          ),
        ],
      ),
    );

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];
      List<dynamic> citas = [];
      try {
        String tempCitas = await PrefsManager.getCitas();
        if (tempCitas.isEmpty) return;
        if (tempCitas.contains('OkRef')) {
          dynamic jsonCitas = json.decode(tempCitas);
          String okRef = jsonCitas['OkRef'];
          Fluttertoast.showToast(
              msg: okRef,
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 3,
              backgroundColor: Colors.black,
              textColor: Colors.white,
              fontSize: 16.0);
        } else {
          citas = json.decode(tempCitas);
          if (citas.isNotEmpty) {
            // Ordenamos las citas por IDCita de mas nuevo a mas viejo
            citas.sort((a, b) => b['IDCita'].compareTo(a['IDCita']));
          }
          // Si no hay citas, mandamos a pantalla de clientes
          if (citas.isEmpty) {
            Fluttertoast.showToast(
                msg:
                    'No tiene citas en su agenda, por favor seleccione un cliente',
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.SNACKBAR,
                timeInSecForIosWeb: 3,
                backgroundColor: Colors.black,
                textColor: Colors.white,
                fontSize: 16.0);
            Navigator.pushNamed(
              context,
              '/ListPage',
              arguments: AppDAClientes.index(
                context,
              ),
            ).then(onGoBack);
          }
        }
      } catch (e) {
        Fluttertoast.showToast(
            msg: e.toString(),
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
            timeInSecForIosWeb: 3,
            backgroundColor: Colors.black,
            textColor: Colors.white,
            fontSize: 16.0);
      }

      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.data = citas;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }
}
