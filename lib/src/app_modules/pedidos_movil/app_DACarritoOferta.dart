import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:no_context_navigation/no_context_navigation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pedidosdipath/src/controllers/pedido_actual_controller.dart';
import 'package:printing/printing.dart';
import 'package:badges/badges.dart' as badges;
import 'package:uuid/uuid.dart';

import '/src/DAPackagesRef.dart';
import '/src/app_config/api_request.dart';
import '/src/controllers/buscador_controller.dart';
import '/src/controllers/citas_controller.dart';
import '/src/controllers/cliente_controller.dart';
import '/src/controllers/lista_precios_controller.dart';
import '/src/controllers/pedidos_offline_controller.dart';
import '/src/controllers/agentes_sucursales_cte_controller.dart';
//import '/src/controllers/opciones_controller.dart';
import '/src/controllers/ticket_controller.dart';
import '/src/controllers/user_config_controller.dart';
import '/src/prefs_manager.dart';
import 'app_DAArticulos.dart';
import 'app_DAClientes.dart';

class AppDACarrito {
  /* Carrito */
  static index(BuildContext context, dynamic data) {
    String _id = data['ID'].toString();
    if (_id.isEmpty || _id == 'null') {
      _id = '${data['Cliente'].toString()}|${data['Nombre'].toString()}';
    }
    String _refID = 'detalleCliente|$_id';

    double _subtotal = 0;
    // ignore: unused_local_variable
    double _impuestos1 = 0;
    double _total = 0;
    double _articulosTotales = 0;
    double _cantidadArticulosJuego = 0;
    double _articulosJuego = 0;
    double _totalCalculadoDetalle = 0;
    double _impuestosCalculadoDetalle = 0;
    bool cantidadesMatch = true;
    double totalCantidadesJuego = 0;
    double totalCantidadesNormales = 0;

    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    PedidoActualController _pedidoActualController =
        Get.put(PedidoActualController());
    String uuid = Uuid().v4();

    BuscadorController _buscadorController = Get.find<BuscadorController>();

    TicketController _ticketController = Get.find<TicketController>();

    _pedidoActualController.init(cliente: _id);
    if (!_pedidoActualController.clienteCoincide(_id)) {
      _pedidoActualController.reset();
      _pedidoActualController.setUuid(uuid);
      _pedidoActualController.setCliente(_id);
      _pedidoActualController.setPedidoEnCurso(true);
      _pedidoActualController.setOfertasCalculadas(false);
      _pedidoActualController
          .addToHistorial(_pedidoActualController.pedidoActual);
    }

    try {
      // ignore: avoid_init_to_null, unused_local_variable
      dynamic currentRow = null;
      List<dynamic> _dtData = [];

      String removeNullMoney(String? value) {
        if (value == null || value == 'null') return '0';
        return value;
      }

      void resetTotalsOfertas() {
        _total = 0;
        _articulosTotales = 0;
        _cantidadArticulosJuego = 0;
        _articulosJuego = 0;
        _totalCalculadoDetalle = 0;
        _impuestosCalculadoDetalle = 0;
        cantidadesMatch = true;
        totalCantidadesJuego = 0;
        totalCantidadesNormales = 0;
      }

      totalize({List<String>? carrito}) async {
        _total = 0;
        _articulosTotales = 0;

        List<String> _articulos =
            carrito ?? await PrefsManager.getCarrito() ?? [];
        String _clienteActual = await PrefsManager.getClienteActual();

        if (carrito != null && carrito.isNotEmpty) {
          // ignore: unnecessary_null_comparison
          if (_articulos != null && _articulos.isNotEmpty) {
            _articulos.forEach((element) {
              dynamic _articulo = json.decode(element);
              if (_articulo['Cliente'] == _clienteActual) {
                if (_articulo['Total'] != null &&
                    _articulo['Total'] != 'null') {
                  _total += double.parse(_articulo['Total'].toString());
                }
                _articulosTotales += double.parse(_articulo['Cantidad']);
              }
            });
          }
        }

        if (_pedidoActualController.pedidoActual.ofertasCalculadas) {
          dynamic temp = double.tryParse(
              _pedidoActualController.pedidoActual.importeOferta);
          if (temp != null) {
            _total = temp;
          } else {
            _total = 0;
          }
        }
      }

      double calculateSum(
          List<dynamic> carrito, String _clienteActual, String field,
          [String renglonTipo = 'J']) {
        return carrito
            .where((element) =>
                element['RenglonTipo'] == renglonTipo &&
                element['Cliente'] == _clienteActual)
            .fold(0, (sum, element) => sum + element[field]);
      }

      double calculateCount(List<dynamic> carrito, String _clienteActual,
          [String renglonTipo = 'J']) {
        return carrito
            .where((element) =>
                element['RenglonTipo'] == renglonTipo &&
                element['Cliente'] == _clienteActual)
            .length
            .toDouble();
      }

      double calculateTotalCantidadesJuego(
          List<dynamic> carrito, String _clienteActual) {
        return carrito
            .where((element) =>
                element['RenglonTipo'] == 'J' &&
                element['Cliente'] == _clienteActual)
            .fold(
                0,
                (sum, element) =>
                    sum +
                    calculateSum(carrito, _clienteActual, 'Cantidad', 'C'));
      }

      void calculateJuegoTotalsOfertas(
          List<dynamic> carrito, String _clienteActual) {
        _cantidadArticulosJuego =
            calculateSum(carrito, _clienteActual, 'Cantidad');
        _articulosJuego = calculateCount(carrito, _clienteActual);
        _totalCalculadoDetalle =
            calculateSum(carrito, _clienteActual, 'Precio');
        _impuestosCalculadoDetalle =
            calculateSum(carrito, _clienteActual, 'Impuesto1');
        totalCantidadesJuego =
            calculateTotalCantidadesJuego(carrito, _clienteActual);
        totalCantidadesNormales =
            calculateSum(carrito, _clienteActual, 'Cantidad', 'N');

        if (totalCantidadesNormales > 0) {
          totalCantidadesJuego += totalCantidadesNormales;
        }
      }

      void validatePrice(dynamic element) {
        if (element['Precio'] == null) {
          element['Precio'] = 0;
        }
        _total += (element['Precio'] * element['Cantidad']);
      }

      void validateQuantity(dynamic element, List<dynamic>? carritoOriginal) {
        if (carritoOriginal != null) {
          carritoOriginal.forEach((elementOriginal) {
            if (elementOriginal['Articulo'] == element['Articulo'] &&
                element['RenglonTipo'] == 'J') {
              if (double.parse(elementOriginal['Cantidad'].toString()) !=
                      element['Cantidad'] &&
                  element['RenglonID'] == elementOriginal['RenglonID']) {
                element['Cantidad'] =
                    double.parse(elementOriginal['Cantidad'].toString());
                cantidadesMatch = false;
              }
            }
          });
        }
      }

      void calculateTotalsOfertas(List<dynamic> carrito, String _clienteActual,
          List<dynamic>? carritoOriginal) {
        carrito.forEach((element) {
          if (element['Cliente'] == _clienteActual) {
            validatePrice(element);
            validateQuantity(element, carritoOriginal);
            _articulosTotales += element['Cantidad'];
          }
        });

        calculateJuegoTotalsOfertas(carrito, _clienteActual);
      }

      void adjustTotalsForMismatchOfertas() {
        if (_articulosJuego == 1 && _cantidadArticulosJuego > 0) {
          _subtotal = _subtotal * _cantidadArticulosJuego;
          _total = _total * _cantidadArticulosJuego;
        }
        if (_articulosJuego > 1) {
          _subtotal = _totalCalculadoDetalle;
          _impuestos1 = _impuestosCalculadoDetalle;
          _total = _totalCalculadoDetalle;
        }
      }

      void applyOfferCalculations() {
        dynamic tempImporte =
            double.tryParse(_pedidoActualController.pedidoActual.importeOferta);
        dynamic tempImpuestos = double.tryParse(
            _pedidoActualController.pedidoActual.impuestosOferta);
        dynamic tempTotal =
            double.tryParse(_pedidoActualController.pedidoActual.totalOferta);
        if (tempImporte != null) {
          _subtotal = tempImporte;
          _impuestos1 = tempImpuestos;
          _total = tempTotal;
          if (!cantidadesMatch) {
            adjustTotalsForMismatchOfertas();
          }
        } else {
          _total != 0 ? _total = _total : _total = 0;
        }
      }

      Future<void> totalizeOfertas(
          {required List<dynamic>? carrito,
          List<dynamic>? carritoOriginal}) async {
        resetTotalsOfertas();

        String _clienteActual = await PrefsManager.getClienteActual();

        if (carrito != null && carrito.isNotEmpty) {
          calculateTotalsOfertas(carrito, _clienteActual, carritoOriginal);
        }

        if (_pedidoActualController.pedidoActual.ofertasCalculadas) {
          applyOfferCalculations();
        }
      }

      Future<FutureOr> onGoBack(Object? value) async {
        totalize();
        //addOptions();

        _formProv.pageRefresh();
      }

      /* String removeNullMoney(String? value) {
        if (value == null || value == 'null') return '0';
        return value;
      } */

      confirmDialog() async {
        List<String>? tempCart = await PrefsManager.getCarrito();
        await totalize(carrito: tempCart);

        String _clienteActual = await PrefsManager.getClienteActual();
        //String _clienteNombre = await PrefsManager.getClienteNombreActual();
        List<dynamic> _cart = [];

        TextEditingController _observacionesController =
            new TextEditingController();

        showDialog(
          context: context,
          builder: (context) {
            if (_articulosTotales > 0) {
              Fluttertoast.showToast(
                  msg: 'Pedido Actual Guardado',
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.SNACKBAR,
                  timeInSecForIosWeb: 1,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0);

              return DAInputDialog(
                title: '¿Está seguro de realizar el pedido?',
                input: [
                  DAInput(
                    refID: 'articulosTotales',
                    controller: new TextEditingController(
                        text: totalCantidadesJuego == 0
                            ? _articulosTotales.toString()
                            : totalCantidadesJuego.toString()),
                    label: 'Artículos Totales',
                    isRequired: false,
                    disabled: true,
                    tipo: DAInputType.string,
                  ),
                  DAInput(
                    refID: 'totalPedido',
                    controller: new TextEditingController(
                        text:
                            '\$ ${_articulosJuego == 0 ? _pedidoActualController.pedidoActual.totalOferta : _articulosJuego == 1 && _cantidadArticulosJuego > 0 ? (_total * _cantidadArticulosJuego).toStringAsFixed(2) : _articulosJuego > 1 ? _totalCalculadoDetalle.toStringAsFixed(2) : _total.toStringAsFixed(2)}'),
                    label: 'Total',
                    isRequired: false,
                    disabled: true,
                    tipo: DAInputType.string,
                  ),
                  DAInput(
                    refID: 'observaciones',
                    controller: _observacionesController,
                    label: 'Observaciones',
                    isRequired: false,
                    tipo: DAInputType.textarea,
                  )
                ],
                okText: 'Si',
                cancelText: 'No',
                onPressed: () async {
                  dynamic _resCart;
                  try {
                    EasyLoading.show(status: 'Procesando Pedido...');
                    String _cliente = await PrefsManager.getClienteActual();
                    EasyLoading.show(status: 'Obteniendo ubicación...');
                    Position _position =
                        await DAController.getCurrentLocation();
                    EasyLoading.show(status: 'Procesando Pedido...');
                    List<String> _tempCart = await PrefsManager.getCarrito();
                    _ticketController.clearProductos();
                    _tempCart.forEach((element) {
                      dynamic _articulo = json.decode(element);
                      if (_articulo['Cliente'] == _clienteActual) {
                        _cart.add({
                          'Renglon': _articulo['Renglon'],
                          'Articulo': _articulo['Articulo'].toString(),
                          'SubCuenta': _articulo['SubCuenta'],
                          'Cantidad': _articulo['Cantidad'].toString(),
                          'Precio': _articulo['Precio'],
                          'Unidad': _articulo['Unidad'].toString(),
                          'DescuentoLinea': _articulo['DescuentoLinea'],
                          'Impuesto1': _articulo['Impuesto1'] ?? 0,
                          'Impuesto2': _articulo['Impuesto2'] ?? 0,
                          'Impuesto3': _articulo['Impuesto3'] ?? 0,
                          'Retencion1': _articulo['Retencion1'] ?? 0,
                          'Retencion2': _articulo['Retencion2'] ?? 0,
                          'Retencion3': _articulo['Retencion3'] ?? 0,
                          'RenglonTipo': _articulo['RenglonTipo'],
                          'DescripcionExtra':
                              _articulo['DescripcionExtra'] ?? '',
                        });

                        _ticketController.addProducto(Producto(
                          articulo: _articulo['Articulo'].toString(),
                          descripcion: _articulo['Descripcion'].toString(),
                          precio: double.parse(_articulo['Precio']),
                          impuesto1: double.parse(removeNullMoney(
                              _articulo['Impuesto1'].toString())),
                          impuesto2: double.parse(removeNullMoney(
                              _articulo['Impuesto2'].toString())),
                          impuesto3: double.parse(removeNullMoney(
                              _articulo['Impuesto3'].toString())),
                          retencion1: double.parse(removeNullMoney(
                              _articulo['Retencion1'].toString())),
                          retencion2: double.parse(removeNullMoney(
                              _articulo['Retencion2'].toString())),
                          retencion3: double.parse(removeNullMoney(
                              _articulo['Retencion3'].toString())),
                          cantidad: double.parse(_articulo['Cantidad']),
                          total: double.parse(_articulo['Precio']) *
                              double.parse(_articulo['Cantidad']),
                        ));
                      }
                    });

                    if (_pedidoActualController
                        .pedidoActual.ofertasCalculadas) {
                      _ticketController.clearProductos();
                      _pedidoActualController.pedidoActual.carritoOferta
                          .forEach((element) {
                        _ticketController.addProducto(Producto(
                          articulo: element['Articulo'].toString(),
                          descripcion: element['Descripcion'].toString(),
                          precio: double.parse(element['Precio'].toString()),
                          impuesto1: double.parse(
                              removeNullMoney(element['Impuesto1'].toString())),
                          impuesto2: double.parse(
                              removeNullMoney(element['Impuesto2'].toString())),
                          impuesto3: double.parse(
                              removeNullMoney(element['Impuesto3'].toString())),
                          retencion1: double.parse(removeNullMoney(
                              element['Retencion1'].toString())),
                          retencion2: double.parse(removeNullMoney(
                              element['Retencion2'].toString())),
                          retencion3: double.parse(removeNullMoney(
                              element['Retencion3'].toString())),
                          cantidad:
                              double.parse(element['Cantidad'].toString()),
                          total: double.parse(element['Precio']) *
                              double.parse(element['Cantidad']),
                        ));
                      });
                    }

                    // Obtenemos el EnviarA basado en la sucursal del cliente seleccionada
                    String? enviarA;
                    final _sucursalesClienteController =
                        Get.find<AgentesSucursalesCteController>();
                    final sucursalSeleccionada = _sucursalesClienteController
                        .getSucursalClienteSeleccionada;
                    if (sucursalSeleccionada != null) {
                      enviarA = sucursalSeleccionada.id?.toString();
                    }

                    // Comenzamos a guardar el pedido de forma offline
                    Map<String, dynamic> _offlinePedido = {
                      'Detalle': _cart,
                      'Cliente': _cliente,
                      'Observaciones': _observacionesController.text,
                      'FechaEmision': DateTime.now().toString(),
                      'Moneda': Get.find<UserConfigController>().monedaUsuario,
                      'TipoCambio': Get.find<UserConfigController>()
                          .usuarioMovil
                          .tipoCambio,
                      'ListaPrecios':
                          Get.find<ListaPreciosController>().listaPrecios,
                      'CondicionPago':
                          Get.find<ClienteController>().getCondicionPago,
                      'EnviarA': enviarA,
                      'Latitud': _position.latitude,
                      'Longitud': _position.longitude,
                    };

                    Get.find<PedidosOfflineController>()
                        .addPedidoOfflineFromJson(_offlinePedido);

                    bool result =
                        await InternetConnectionChecker().hasConnection;
                    if (result) {
                      DARequestModel _reqCart = ApiRequest.pedido(
                          cliente: _cliente,
                          idCita: Get.find<CitasController>().getIdCita,
                          listaPrecios:
                              Get.find<ListaPreciosController>().listaPrecios,
                          condicionPago:
                              Get.find<ClienteController>().getCondicionPago,
                          lat: _position.latitude,
                          lng: _position.longitude,
                          carrito: _pedidoActualController
                                  .pedidoActual.ofertasCalculadas
                              ? _pedidoActualController
                                  .pedidoActual.carritoOferta
                              : _cart,
                          enviarA: enviarA,
                          observaciones: _observacionesController.text);
                      _resCart = await ApiRequest.execAPI(
                          _reqCart.uriReq, _reqCart.bodyReq,
                          update: true);

                      Get.find<CitasController>().reset();
                      EasyLoading.dismiss();
                    } else {
                      EasyLoading.dismiss();
                      Fluttertoast.showToast(
                          msg:
                              'No hay conexión a internet, el pedido se guardará de forma offline.',
                          toastLength: Toast.LENGTH_LONG,
                          gravity: ToastGravity.SNACKBAR,
                          timeInSecForIosWeb: 3,
                          backgroundColor: Colors.black,
                          textColor: Colors.white,
                          fontSize: 16.0);
                      Get.find<CitasController>().reset();
                      await PrefsManager.setCarritoVacio();
                      //navService.pushNamedAndRemoveUntil('home');
                    }
                  } catch (e) {
                    EasyLoading.dismiss();
                    Fluttertoast.showToast(
                        msg: 'Error: $e\nEl pedido ha sido guardado.',
                        toastLength: Toast.LENGTH_LONG,
                        gravity: ToastGravity.SNACKBAR,
                        timeInSecForIosWeb: 3,
                        backgroundColor: Colors.black,
                        textColor: Colors.white,
                        fontSize: 16.0);

                    navService.pushNamedAndRemoveUntil('home');
                    return;
                  }

                  // Validamos si hay internet, en caso de no haber, ejecutamos creacion de ticket automaticamente.
                  bool result = await InternetConnectionChecker().hasConnection;
                  if (!result || _resCart['Ok'] == null) {
                    _ticketController
                        .setMovimiento(result ? _resCart['Mov'] : 'Pedido');
                    _ticketController
                        .setMovimientoID(result ? _resCart['MovID'] : '');
                    if (!result) {
                      // Ejecución automática de la lógica normalmente en onCancelPressed

                      final pdf = pw.Document();

                      final netLogoImage = await imageFromAssetBundle(
                          'assets/app/logo-ticket.png');

                      // Función para dar formato de fecha con hora y minuto como se usa en México
                      String _formatDate(DateTime date) {
                        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
                      }

                      pw.Widget _header(pw.Context context) {
                        return pw.Container(
                          child: pw.Column(
                            children: [
                              // Se agrega row con logo y datos de la empresa
                              pw.Row(
                                children: [
                                  pw.Container(
                                    width: 200,
                                    height: 200,
                                    margin: pw.EdgeInsets.only(right: 25),
                                    child: pw.Image(netLogoImage),
                                  ),
                                  pw.Container(
                                    width: 300,
                                    child: pw.Column(
                                      children: [
                                        pw.Text(
                                          '${_ticketController.clienteNombre}',
                                          style: pw.TextStyle(
                                            fontSize: 20,
                                            fontWeight: pw.FontWeight.bold,
                                          ),
                                        ),
                                        pw.Text(
                                          '${_ticketController.clienteDireccion}',
                                          style: pw.TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        pw.Text(
                                          '${_ticketController.clienteTelefono}',
                                          style: pw.TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                        pw.Text(
                                          '${_ticketController.clienteCorreo}',
                                          style: pw.TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              pw.Row(
                                children: [
                                  pw.Container(
                                    width: 200,
                                    height: 50,
                                    margin: pw.EdgeInsets.only(top: 25),
                                    padding: pw.EdgeInsets.all(5),
                                    decoration: pw.BoxDecoration(
                                      borderRadius: const pw.BorderRadius.all(
                                          pw.Radius.circular(2)),
                                      color: PdfColor.fromInt(0xFF0ABCDE),
                                    ),
                                    child: pw.GridView(
                                      crossAxisCount: 2,
                                      children: [
                                        pw.Text(
                                            '${_ticketController.movimiento}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text(
                                            '${_ticketController.movimientoID}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('Fecha:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text(_formatDate(DateTime.now()),
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                      ],
                                    ),
                                  ),
                                  pw.SizedBox(width: 50),
                                ],
                              ),
                              pw.SizedBox(height: 20),
                            ],
                          ),
                        );
                      }

                      pw.Widget _body(pw.Context context) {
                        const tableHeaders = [
                          'Artículo',
                          'Descripción',
                          'Precio',
                          'Cantidad',
                          'Total',
                        ];

                        return pw.TableHelper.fromTextArray(
                          border: null,
                          cellAlignment: pw.Alignment.centerLeft,
                          headerDecoration: pw.BoxDecoration(
                            borderRadius: const pw.BorderRadius.all(
                                pw.Radius.circular(2)),
                            color: PdfColor.fromInt(0xFF0ABCDE),
                          ),
                          headerHeight: 25,
                          cellHeight: 40,
                          cellAlignments: {
                            0: pw.Alignment.centerLeft,
                            1: pw.Alignment.centerLeft,
                            2: pw.Alignment.centerRight,
                            3: pw.Alignment.center,
                            4: pw.Alignment.centerRight,
                          },
                          headerStyle: pw.TextStyle(
                            color: PdfColors.white,
                            fontSize: 10,
                            fontWeight: pw.FontWeight.bold,
                          ),
                          cellStyle: const pw.TextStyle(
                            color: PdfColors.black,
                            fontSize: 10,
                          ),
                          rowDecoration: pw.BoxDecoration(
                            border: pw.Border(
                              bottom: pw.BorderSide(
                                color: PdfColor.fromInt(0xFF00DDCB),
                                width: .5,
                              ),
                            ),
                          ),
                          headers: List<String>.generate(
                            tableHeaders.length,
                            (col) => tableHeaders[col],
                          ),
                          data: List<List<String>>.generate(
                            _ticketController.productos.length,
                            (row) => List<String>.generate(
                              tableHeaders.length,
                              (col) => _ticketController.productos[row]
                                  .getIndex(col),
                            ),
                          ),
                        );
                      }

                      pw.Widget _bodyFooter(pw.Context context) {
                        return pw.Container(
                          child: pw.Column(
                            children: [
                              pw.SizedBox(height: 20),
                              pw.Row(
                                children: [
                                  pw.Container(
                                    width: 200,
                                    height: 50,
                                    margin: pw.EdgeInsets.only(top: 25),
                                    padding: pw.EdgeInsets.all(5),
                                    decoration: pw.BoxDecoration(
                                      borderRadius: const pw.BorderRadius.all(
                                          pw.Radius.circular(2)),
                                      color: PdfColor.fromInt(0xFF0ABCDE),
                                    ),
                                    child: pw.GridView(
                                      crossAxisCount: 2,
                                      children: [
                                        pw.Text('Subtotal:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('${_ticketController.importe}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('IVA:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('${_ticketController.impuesto}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('Retención:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text(
                                            '${_ticketController.retencion}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('Total:',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                        pw.Text('${_ticketController.total}',
                                            style: pw.TextStyle(
                                                color: PdfColors.white)),
                                      ],
                                    ),
                                  ),
                                  pw.SizedBox(width: 50),
                                ],
                              ),
                              pw.SizedBox(height: 20),
                              pw.Text(
                                'Gracias por su compra',
                                style: pw.TextStyle(
                                  fontSize: 20,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      pw.Widget _footer(pw.Context context) {
                        return pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                          children: [
                            pw.Container(
                              height: 20,
                              width: 100,
                              child: pw.BarcodeWidget(
                                barcode: pw.Barcode.pdf417(),
                                data:
                                    '${_ticketController.movimiento} ${_ticketController.movimientoID}',
                                drawText: false,
                              ),
                            ),
                            pw.Text(
                              'Pagina ${context.pageNumber}/${context.pagesCount}',
                              style: const pw.TextStyle(
                                fontSize: 12,
                                color: PdfColors.black,
                              ),
                            ),
                          ],
                        );
                      }

                      pdf.addPage(
                        pw.MultiPage(
                          pageFormat: PdfPageFormat.a4,
                          header: _header,
                          footer: _footer,
                          build: (pw.Context context) => [
                            /* _header,
                      pw.SizedBox(height: 20), */
                            _body(context),
                            _bodyFooter(context),
                          ],
                        ),
                      );

                      await PrefsManager.setCarritoHistorial(
                        cart: _cart,
                        clienteActual: _clienteActual,
                      );

                      await PrefsManager.setCarritoVacio();

                      await Printing.sharePdf(
                          bytes: await pdf.save(),
                          filename:
                              '${_ticketController.movimiento} ${_ticketController.movimientoID}.pdf');
                      _ticketController.reset();

                      navService.pushNamedAndRemoveUntil('home');
                    } else {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return DAInputDialog(
                            title:
                                '${_resCart['Mov']} ${_resCart['MovID']} generado correctamente.',
                            subtitle:
                                'Si desea compartir el ticket, presione el botón de ticket.',
                            input: [],
                            okText: 'Aceptar',
                            cancelText: 'Ticket',
                            onPressed: () async {
                              Navigator.of(context).pop();
                              Navigator.of(context).pop();
                              await PrefsManager.setCarritoHistorial(
                                cart: _cart,
                                clienteActual: _clienteActual,
                              );
                              await PrefsManager.setCarritoVacio();
                              Get.find<PedidosOfflineController>().reset();
                              onGoBack(null);
                              navService.pushNamedAndRemoveUntil('home');
                            },
                            onCancelPressed: () async {
                              final pdf = pw.Document();

                              final netLogoImage = await imageFromAssetBundle(
                                  'assets/app/logo-ticket.png');

                              // Función para dar formato de fecha con hora y minuto como se usa en México
                              String _formatDate(DateTime date) {
                                return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
                              }

                              pw.Widget _header(pw.Context context) {
                                return pw.Container(
                                  child: pw.Column(
                                    children: [
                                      // Se agrega row con logo y datos de la empresa
                                      pw.Row(
                                        children: [
                                          pw.Container(
                                            width: 200,
                                            height: 200,
                                            margin:
                                                pw.EdgeInsets.only(right: 25),
                                            child: pw.Image(netLogoImage),
                                          ),
                                          pw.Container(
                                            width: 300,
                                            child: pw.Column(
                                              children: [
                                                pw.Text(
                                                  '${_ticketController.clienteNombre}',
                                                  style: pw.TextStyle(
                                                    fontSize: 20,
                                                    fontWeight:
                                                        pw.FontWeight.bold,
                                                  ),
                                                ),
                                                pw.Text(
                                                  '${_ticketController.clienteDireccion}',
                                                  style: pw.TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                                pw.Text(
                                                  '${_ticketController.clienteTelefono}',
                                                  style: pw.TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                                pw.Text(
                                                  '${_ticketController.clienteCorreo}',
                                                  style: pw.TextStyle(
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      pw.Row(
                                        children: [
                                          pw.Container(
                                            width: 200,
                                            height: 50,
                                            margin: pw.EdgeInsets.only(top: 25),
                                            padding: pw.EdgeInsets.all(5),
                                            decoration: pw.BoxDecoration(
                                              borderRadius:
                                                  const pw.BorderRadius.all(
                                                      pw.Radius.circular(2)),
                                              color:
                                                  PdfColor.fromInt(0xFF0ABCDE),
                                            ),
                                            child: pw.GridView(
                                              crossAxisCount: 2,
                                              children: [
                                                pw.Text(
                                                    '${_ticketController.movimiento}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.movimientoID}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('Fecha:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    _formatDate(DateTime.now()),
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                              ],
                                            ),
                                          ),
                                          pw.SizedBox(width: 50),
                                        ],
                                      ),
                                      pw.SizedBox(height: 20),
                                    ],
                                  ),
                                );
                              }

                              pw.Widget _body(pw.Context context) {
                                const tableHeaders = [
                                  'Artículo',
                                  'Descripción',
                                  'Precio',
                                  'Cantidad',
                                  'Total',
                                ];

                                return pw.TableHelper.fromTextArray(
                                  border: null,
                                  cellAlignment: pw.Alignment.centerLeft,
                                  headerDecoration: pw.BoxDecoration(
                                    borderRadius: const pw.BorderRadius.all(
                                        pw.Radius.circular(2)),
                                    color: PdfColor.fromInt(0xFF0ABCDE),
                                  ),
                                  headerHeight: 25,
                                  cellHeight: 40,
                                  cellAlignments: {
                                    0: pw.Alignment.centerLeft,
                                    1: pw.Alignment.centerLeft,
                                    2: pw.Alignment.centerRight,
                                    3: pw.Alignment.center,
                                    4: pw.Alignment.centerRight,
                                  },
                                  headerStyle: pw.TextStyle(
                                    color: PdfColors.white,
                                    fontSize: 10,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                  cellStyle: const pw.TextStyle(
                                    color: PdfColors.black,
                                    fontSize: 10,
                                  ),
                                  rowDecoration: pw.BoxDecoration(
                                    border: pw.Border(
                                      bottom: pw.BorderSide(
                                        color: PdfColor.fromInt(0xFF00DDCB),
                                        width: .5,
                                      ),
                                    ),
                                  ),
                                  headers: List<String>.generate(
                                    tableHeaders.length,
                                    (col) => tableHeaders[col],
                                  ),
                                  data: List<List<String>>.generate(
                                    _ticketController.productos.length,
                                    (row) => List<String>.generate(
                                      tableHeaders.length,
                                      (col) => _ticketController.productos[row]
                                          .getIndex(col),
                                    ),
                                  ),
                                );
                              }

                              pw.Widget _bodyFooter(pw.Context context) {
                                return pw.Container(
                                  child: pw.Column(
                                    children: [
                                      pw.SizedBox(height: 20),
                                      pw.Row(
                                        children: [
                                          pw.Container(
                                            width: 200,
                                            height: 50,
                                            margin: pw.EdgeInsets.only(top: 25),
                                            padding: pw.EdgeInsets.all(5),
                                            decoration: pw.BoxDecoration(
                                              borderRadius:
                                                  const pw.BorderRadius.all(
                                                      pw.Radius.circular(2)),
                                              color:
                                                  PdfColor.fromInt(0xFF0ABCDE),
                                            ),
                                            child: pw.GridView(
                                              crossAxisCount: 2,
                                              children: [
                                                pw.Text('Subtotal:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalSubtotal}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('IVA:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalIva}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('Retención:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalRetencion}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text('Total:',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                                pw.Text(
                                                    '${_ticketController.totalPrice}',
                                                    style: pw.TextStyle(
                                                        color:
                                                            PdfColors.white)),
                                              ],
                                            ),
                                          ),
                                          pw.SizedBox(width: 50),
                                        ],
                                      ),
                                      pw.SizedBox(height: 20),
                                      pw.Text(
                                        'Gracias por su compra',
                                        style: pw.TextStyle(
                                          fontSize: 20,
                                          fontWeight: pw.FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }

                              pw.Widget _footer(pw.Context context) {
                                return pw.Row(
                                  mainAxisAlignment:
                                      pw.MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                                  children: [
                                    pw.Container(
                                      height: 20,
                                      width: 100,
                                      child: pw.BarcodeWidget(
                                        barcode: pw.Barcode.pdf417(),
                                        data:
                                            '${_ticketController.movimiento} ${_ticketController.movimientoID}',
                                        drawText: false,
                                      ),
                                    ),
                                    pw.Text(
                                      'Pagina ${context.pageNumber}/${context.pagesCount}',
                                      style: const pw.TextStyle(
                                        fontSize: 12,
                                        color: PdfColors.black,
                                      ),
                                    ),
                                  ],
                                );
                              }

                              pdf.addPage(
                                pw.MultiPage(
                                  pageFormat: PdfPageFormat.a4,
                                  header: _header,
                                  footer: _footer,
                                  build: (pw.Context context) => [
                                    /* _header,
                      pw.SizedBox(height: 20), */
                                    _body(context),
                                    _bodyFooter(context),
                                  ],
                                ),
                              );

                              await PrefsManager.setCarritoHistorial(
                                cart: _cart,
                                clienteActual: _clienteActual,
                              );

                              await PrefsManager.setCarritoVacio();

                              await Printing.sharePdf(
                                  bytes: await pdf.save(),
                                  filename:
                                      '${_ticketController.movimiento} ${_ticketController.movimientoID}.pdf');
                              _ticketController.reset();
                              Get.find<PedidosOfflineController>().reset();

                              navService.pushNamedAndRemoveUntil('home');
                            },
                          );
                        },
                      );
                    }
                  } else {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return DAInputDialog(
                          title: 'Error al generar el pedido',
                          input: [],
                          okText: 'Aceptar',
                          cancelText: '',
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                        );
                      },
                    );
                  }
                },
              );
            } else {
              return DAInputDialog(
                title:
                    'No hay artículos en el carrito para realizar el pedido.',
                input: [],
                okText: 'Aceptar',
                cancelText: '',
                onPressed: () async {
                  Navigator.of(context).pop();
                },
              );
            }
          },
        );
      }

      _finderAdd() async {
        String tempArticles = await PrefsManager.getArticulos();
        List<dynamic> articulos = json.decode(tempArticles);
        final articulosOriginal = List.unmodifiable(articulos);
        PrefsManager.clearLastFinderFilteredData();
        PrefsManager.clearFinderFilters();

        _buscadorController.articulos.forEach((articuloAgregado) {
          articulos.forEach((articulo) {
            if (articulo['Articulo'] == articuloAgregado.articulo) {
              articulo['checked'] = true;
            }
          });
        });

        showSearch(
          context: context,
          delegate: DASearchDelegateOfflineMultiple(
            closeOnTap: false,
            minLengthQuery: 3,
            //keyFinderID: 'artFinder',
            dataSource: articulos,
            dataSourceSelected: _buscadorController.articulosJson,
            dataOriginal: articulosOriginal,
            configTiles: DAFinderCardModel(
              title: "Descripcion1",
              leading: "Articulo",
              trailing: "Codigo",
              icon: "icon",
              moreLabels: ["Existencia", "Unidad"],
              filterLabels: [
                "Categoria",
                "Grupo",
                "Familia",
                "Linea",
                "Tipo",
                "TipoOpcion"
              ],
            ),
            searchLabel: "Buscar Artículos...",
            noDataLabel: "Artículos",
            scannerFinder: true,
            onFinderTap: (value) async {
              dynamic args = value;
              _buscadorController.prepararJuego(args);

              _buscadorController.addArticulo(Articulo.fromJson(args));

              articulos.forEach((articulo) {
                articulo['checked'] = false;
              });

              _buscadorController.articulos.forEach((articuloAgregado) {
                articulos.forEach((articulo) {
                  if (articulo['Articulo'] == articuloAgregado.articulo) {
                    articulo['checked'] = true;
                  }
                });
              });

              Fluttertoast.cancel();

              Fluttertoast.showToast(
                  msg: _buscadorController.mostrarMensaje(),
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.SNACKBAR,
                  timeInSecForIosWeb: 3,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0);

              /* Navigator.pushNamed(
                context,
                '/WidgetsForm',
                arguments: AppDAArticulos.informacion(context, args),
              ).then(onGoBack); */
            },
            onDoneTap: (p0) {
              Navigator.of(context).pop();
              Navigator.pushNamed(
                context,
                '/WidgetsForm',
                arguments: AppDAArticulos.informacionMultiple(context),
              ).then(onGoBack);
            },
          ),
        );
      }

      DALayoutFormFilters _modelLayoutCarrito = new DALayoutFormFilters(
        refID: _refID,
        prefix: 'Carrito de Compras',
        title: data['Cliente'],
        hasBackButton: true,
        iconSubmit: Icons.send,
        formSubmit: (value) async {
          await confirmDialog();
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(children: [
          DABottomAppBarButton(
            label: 'Vaciar',
            icon: Icons.delete,
            onTap: () async {
              List<String>? tempCart = await PrefsManager.getCarrito();
              String _clienteActual = await PrefsManager.getClienteActual();
              bool _isEmpty = true;

              if (tempCart != null && tempCart.isNotEmpty) {
                tempCart.forEach(
                  (element) {
                    dynamic articulo = json.decode(element);
                    if (articulo['Cliente'] == _clienteActual) {
                      _isEmpty = false;
                    }
                  },
                );
              }

              if (_isEmpty) {
                showDialog(
                  context: context,
                  builder: (context) {
                    return DAInputDialog(
                      title: 'No hay artículos en el carrito.',
                      input: [],
                      okText: 'Aceptar',
                      cancelText: '',
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    );
                  },
                );
              } else {
                showDialog(
                  context: context,
                  builder: (context) {
                    return DAInputDialog(
                      title: '¿Está seguro de vaciar el carrito?',
                      input: [],
                      okText: 'Sí',
                      cancelText: 'No',
                      onPressed: () async {
                        Navigator.of(context).pop();
                        await PrefsManager.setCarritoVacio();
                        _pedidoActualController.setOfertasCalculadas(false);
                        _pedidoActualController.resetEliminado();
                        onGoBack(null);
                      },
                      onCancelPressed: () {
                        Navigator.of(context).pop();
                      },
                    );
                  },
                );
              }
            },
          ),
          DABottomAppBarButton(
            label: 'Agregar',
            icon: Icons.add,
            onTap: () async {
              bool _mensajeLeido = false;
              if (_pedidoActualController.pedidoActual.ofertasCalculadas) {
                await showDialog(
                    context: context,
                    builder: ((context) {
                      return DAInputDialog(
                        title: 'Atención',
                        subtitle:
                            'Ofertas ya calculadas. Agregar un artículo reestablecerá su carrito a los precios originales. ¿Desea agregar el artículo?',
                        input: [],
                        okText: 'Sí',
                        cancelText: 'No',
                        onPressed: () async {
                          Navigator.of(context).pop();
                          _mensajeLeido = true;
                          _pedidoActualController.setOfertasCalculadas(false);
                          await totalizeOfertas(
                              carrito: _pedidoActualController
                                  .pedidoActual.carritoOriginal);
                          _pedidoActualController.resetModificado();
                          _formProv.pageRefresh();
                        },
                        onCancelPressed: () {
                          Navigator.of(context).pop();
                        },
                      );
                    }));
              }
              if (_mensajeLeido ||
                  !_pedidoActualController.pedidoActual.ofertasCalculadas) {
                _finderAdd();
                onGoBack(null);
              }
            },
          ),
          _pedidoActualController.pedidoActual.ofertasCalculadas
              ? badges.Badge(
                  badgeContent: Container(),
                  badgeStyle: badges.BadgeStyle(
                    badgeColor: Theme.of(context).primaryColor,
                  ),
                  position: badges.BadgePosition.topStart(top: 4, start: 96),
                  child: DABottomAppBarButton(
                    label: 'Ofertas',
                    icon: Icons.cloud_download,
                    onTap: () async {
                      List<String>? _tempCart = await PrefsManager.getCarrito();

                      if (_tempCart == null || _tempCart.isEmpty) {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return DAInputDialog(
                              title: 'No hay artículos en el carrito.',
                              input: [],
                              okText: 'Aceptar',
                              cancelText: '',
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      } else {
                        if (_pedidoActualController
                            .pedidoActual.ofertasCalculadas) {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return DAInputDialog(
                                title: 'Ya se han calculado las ofertas.',
                                input: [],
                                okText: 'Aceptar',
                                cancelText: '',
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              );
                            },
                          );
                        } else {
                          showDialog(
                            context: context,
                            builder: (context) {
                              return DAInputDialog(
                                title: 'Confirmar acción',
                                subtitle:
                                    'Para esta acción es requerido registrar previamente el pedido en el ERP, ¿desea continuar?',
                                input: [],
                                okText: 'Sí',
                                cancelText: 'No',
                                onPressed: () async {
                                  try {
                                    EasyLoading.show(
                                        status: 'Procesando Ofertas...');
                                    String _cliente =
                                        await PrefsManager.getClienteActual();
                                    List<String>? _tempCart =
                                        await PrefsManager.getCarrito();
                                    List _cart = [];
                                    if (_tempCart != null &&
                                        _tempCart.isNotEmpty) {
                                      _tempCart.forEach((element) {
                                        dynamic _articulo =
                                            json.decode(element);
                                        if (_articulo['Cliente'] == _cliente) {
                                          _cart.add({
                                            'Renglon': _articulo['Renglon'],
                                            'RenglonID': _articulo['RenglonID'],
                                            'RenglonTipo':
                                                _articulo['RenglonTipo'],
                                            'Articulo': _articulo['Articulo']
                                                .toString(),
                                            'SubCuenta': _articulo['SubCuenta'],
                                            'Cantidad': _articulo['Cantidad']
                                                .toString(),
                                            'Precio': _articulo['Precio'],
                                            'Unidad':
                                                _articulo['Unidad'].toString(),
                                            'DescuentoLinea':
                                                _articulo['DescuentoLinea'],
                                            'Impuesto1': _articulo['Impuesto1'],
                                            'Impuesto2': _articulo['Impuesto2'],
                                            'Impuesto3': _articulo['Impuesto3'],
                                            'Retencion1':
                                                _articulo['Retencion1'],
                                            'Retencion2':
                                                _articulo['Retencion2'],
                                            'Retencion3':
                                                _articulo['Retencion3'],
                                            'DescripcionExtra':
                                                _articulo['DescripcionExtra'] ??
                                                    '',
                                          });
                                        }
                                      });
                                      _pedidoActualController
                                          .setCarritoOriginal(_cart);
                                      if (_pedidoActualController
                                          .pedidoActual.id.isEmpty) {
                                        DARequestModel _reqPreparaOferta =
                                            ApiRequest.preparaOfertas(
                                                cliente: _pedidoActualController
                                                    .pedidoActual.cliente,
                                                carrito: _cart,
                                                almacen: Get.find<
                                                            UserConfigController>()
                                                        .usuarioMovil
                                                        .dispAlmacen ??
                                                    '',
                                                listaPrecios: Get.find<
                                                        ListaPreciosController>()
                                                    .listaPrecios);
                                        dynamic _resPreparaOferta =
                                            await ApiRequest.execAPI(
                                                _reqPreparaOferta.uriReq,
                                                _reqPreparaOferta.bodyReq,
                                                update: true);
                                        _pedidoActualController.setId(
                                            _resPreparaOferta['ID'].toString());
                                        _pedidoActualController.setFechaEmision(
                                            _resPreparaOferta['FechaEmision']);
                                        _pedidoActualController.setAlmacen(
                                            _resPreparaOferta['Almacen']);
                                      }
                                      DARequestModel _reqOferta =
                                          ApiRequest.getOfertas(
                                        id: _pedidoActualController
                                            .pedidoActual.id,
                                        listaPrecios:
                                            Get.find<ListaPreciosController>()
                                                .listaPrecios,
                                        fechaEmision: _pedidoActualController
                                            .pedidoActual.fechaEmision,
                                        cliente: _cliente,
                                        concepto: '',
                                        almacen: _pedidoActualController
                                            .pedidoActual.almacen,
                                        detalle: _cart,
                                      );
                                      _reqOferta.bodyReq!['DescuentoGlobal'] =
                                          null;
                                      dynamic _resOferta =
                                          await ApiRequest.execAPI(
                                              _reqOferta.uriReq,
                                              _reqOferta.bodyReq);
                                      _pedidoActualController
                                          .setOfertasCalculadas(true);
                                      _resOferta['detalle'].forEach((element) {
                                        element['Cliente'] = _cliente;
                                      });
                                      _pedidoActualController.setImporteOferta(
                                          _resOferta['encabezado'][0]['Importe']
                                              .toString());
                                      _pedidoActualController
                                          .setImpuestosOferta(
                                              _resOferta['encabezado'][0]
                                                      ['Impuestos']
                                                  .toString());
                                      _pedidoActualController.setTotalOferta(
                                          _resOferta['encabezado'][0]['Total']
                                              .toString());
                                      _pedidoActualController.setCarritoOferta(
                                          _resOferta['detalle']);
                                      EasyLoading.dismiss();
                                      Navigator.of(context).pop();
                                      await totalizeOfertas(
                                          carrito: _pedidoActualController
                                              .pedidoActual.carritoOferta);
                                      _formProv.pageRefresh();
                                    } else {
                                      EasyLoading.dismiss();
                                      Fluttertoast.showToast(
                                          msg:
                                              'No hay artículos en el carrito para calcular ofertas.',
                                          toastLength: Toast.LENGTH_LONG,
                                          gravity: ToastGravity.SNACKBAR,
                                          timeInSecForIosWeb: 3,
                                          backgroundColor: Colors.black,
                                          textColor: Colors.white,
                                          fontSize: 16.0);
                                      Navigator.of(context).pop();
                                    }
                                  } catch (e) {
                                    EasyLoading.dismiss();
                                    Fluttertoast.showToast(
                                        msg: e.toString(),
                                        toastLength: Toast.LENGTH_LONG,
                                        gravity: ToastGravity.SNACKBAR,
                                        timeInSecForIosWeb: 3,
                                        backgroundColor: Colors.black,
                                        textColor: Colors.white,
                                        fontSize: 16.0);
                                    Navigator.of(context).pop();
                                  }
                                },
                                onCancelPressed: () {
                                  Navigator.of(context).pop();
                                },
                              );
                            },
                          );
                        }
                      }
                    },
                  ),
                )
              : DABottomAppBarButton(
                  label: 'Ofertas',
                  icon: Icons.cloud_download,
                  onTap: () async {
                    List<String>? _tempCart = await PrefsManager.getCarrito();

                    if (_tempCart == null || _tempCart.isEmpty) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return DAInputDialog(
                            title: 'No hay artículos en el carrito.',
                            input: [],
                            okText: 'Aceptar',
                            cancelText: '',
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          );
                        },
                      );
                    } else {
                      if (_pedidoActualController
                          .pedidoActual.ofertasCalculadas) {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return DAInputDialog(
                              title: 'Ya se han calculado las ofertas.',
                              input: [],
                              okText: 'Aceptar',
                              cancelText: '',
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      } else {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return DAInputDialog(
                              title: 'Confirmar acción',
                              subtitle:
                                  'Para esta acción es requerido registrar previamente el pedido en el ERP, ¿desea continuar?',
                              input: [],
                              okText: 'Sí',
                              cancelText: 'No',
                              onPressed: () async {
                                try {
                                  EasyLoading.show(
                                      status: 'Procesando Ofertas...');
                                  String _cliente =
                                      await PrefsManager.getClienteActual();
                                  List<String>? _tempCart =
                                      await PrefsManager.getCarrito();
                                  List _cart = [];
                                  if (_tempCart != null &&
                                      _tempCart.isNotEmpty) {
                                    _tempCart.forEach((element) {
                                      dynamic _articulo = json.decode(element);
                                      if (_articulo['Cliente'] == _cliente) {
                                        _cart.add({
                                          'Renglon': _articulo['Renglon'],
                                          'RenglonID': _articulo['RenglonID'],
                                          'RenglonTipo':
                                              _articulo['RenglonTipo'],
                                          'Articulo':
                                              _articulo['Articulo'].toString(),
                                          'SubCuenta': _articulo['SubCuenta'],
                                          'Cantidad':
                                              _articulo['Cantidad'].toString(),
                                          'Precio': _articulo['Precio'],
                                          'Unidad':
                                              _articulo['Unidad'].toString(),
                                          'DescuentoLinea':
                                              _articulo['DescuentoLinea'],
                                          'Impuesto1': _articulo['Impuesto1'],
                                          'Impuesto2': _articulo['Impuesto2'],
                                          'Impuesto3': _articulo['Impuesto3'],
                                          'Retencion1': _articulo['Retencion1'],
                                          'Retencion2': _articulo['Retencion2'],
                                          'Retencion3': _articulo['Retencion3'],
                                        });
                                      }
                                    });
                                    _pedidoActualController
                                        .setCarritoOriginal(_cart);
                                    if (_pedidoActualController
                                        .pedidoActual.id.isEmpty) {
                                      DARequestModel _reqPreparaOferta =
                                          ApiRequest.preparaOfertas(
                                              cliente: _pedidoActualController
                                                  .pedidoActual.cliente,
                                              carrito: _cart,
                                              almacen: Get.find<
                                                          UserConfigController>()
                                                      .usuarioMovil
                                                      .dispAlmacen ??
                                                  '',
                                              listaPrecios: Get.find<
                                                      ListaPreciosController>()
                                                  .listaPrecios);
                                      dynamic _resPreparaOferta =
                                          await ApiRequest.execAPI(
                                              _reqPreparaOferta.uriReq,
                                              _reqPreparaOferta.bodyReq,
                                              update: true);
                                      _pedidoActualController.setId(
                                          _resPreparaOferta['ID'].toString());
                                      _pedidoActualController.setFechaEmision(
                                          _resPreparaOferta['FechaEmision']);
                                      _pedidoActualController.setAlmacen(
                                          _resPreparaOferta['Almacen']);
                                    }
                                    DARequestModel _reqOferta =
                                        ApiRequest.getOfertas(
                                      id: _pedidoActualController
                                          .pedidoActual.id,
                                      listaPrecios:
                                          Get.find<ListaPreciosController>()
                                              .listaPrecios,
                                      fechaEmision: _pedidoActualController
                                          .pedidoActual.fechaEmision,
                                      cliente: _cliente,
                                      concepto: '',
                                      almacen: _pedidoActualController
                                          .pedidoActual.almacen,
                                      detalle: _cart,
                                    );
                                    _reqOferta.bodyReq!['DescuentoGlobal'] =
                                        null;
                                    dynamic _resOferta =
                                        await ApiRequest.execAPI(
                                            _reqOferta.uriReq,
                                            _reqOferta.bodyReq);
                                    _pedidoActualController
                                        .setOfertasCalculadas(true);
                                    _resOferta['detalle'].forEach((element) {
                                      element['Cliente'] = _cliente;
                                    });
                                    _pedidoActualController.setImporteOferta(
                                        _resOferta['encabezado'][0]['Importe']
                                            .toString());
                                    _pedidoActualController.setImpuestosOferta(
                                        _resOferta['encabezado'][0]['Impuestos']
                                            .toString());
                                    _pedidoActualController.setTotalOferta(
                                        _resOferta['encabezado'][0]['Total']
                                            .toString());
                                    _pedidoActualController.setCarritoOferta(
                                        _resOferta['detalle']);
                                    EasyLoading.dismiss();
                                    Navigator.of(context).pop();
                                    await totalizeOfertas(
                                        carrito: _pedidoActualController
                                            .pedidoActual.carritoOferta);
                                    _formProv.pageRefresh();
                                  } else {
                                    EasyLoading.dismiss();
                                    Fluttertoast.showToast(
                                        msg:
                                            'No hay artículos en el carrito para calcular ofertas.',
                                        toastLength: Toast.LENGTH_LONG,
                                        gravity: ToastGravity.SNACKBAR,
                                        timeInSecForIosWeb: 3,
                                        backgroundColor: Colors.black,
                                        textColor: Colors.white,
                                        fontSize: 16.0);
                                    Navigator.of(context).pop();
                                  }
                                } catch (e) {
                                  EasyLoading.dismiss();
                                  Fluttertoast.showToast(
                                      msg: e.toString(),
                                      toastLength: Toast.LENGTH_LONG,
                                      gravity: ToastGravity.SNACKBAR,
                                      timeInSecForIosWeb: 3,
                                      backgroundColor: Colors.black,
                                      textColor: Colors.white,
                                      fontSize: 16.0);
                                  Navigator.of(context).pop();
                                }
                              },
                              onCancelPressed: () {
                                Navigator.of(context).pop();
                              },
                            );
                          },
                        );
                      }
                    }
                  },
                ),
          DABottomAppBarButton(
            label: '               ',
            onTap: null,
          ),
        ]),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        DADataTablePro _dtWidget = DADataTablePro(
          refID: 'dtXAfectar',
          scopeRowID: 'ID',
          noDataMsg: 'artículos',
          hasFinder: true,
          columnsDef: <DAConfigRowModel>[
            DAConfigRowModel(
              label: "Artículo",
              scope: "Articulo",
              isnumeric: false,
            ),
            DAConfigRowModel(
              label: "Total",
              scope: "TotalVisual",
            ),
            DAConfigRowModel(
              label: "Cantidad",
              scope: "Cantidad",
            ),
            DAConfigRowModel(
              label: "Unidad",
              scope: "Unidad",
            ),
            DAConfigRowModel(
              label: "Precio Unitario",
              scope: "Precio",
            ),
            DAConfigRowModel(
              label: "Descuento",
              scope: "DescuentoLinea",
            ),
            DAConfigRowModel(
              label: "Tipo",
              scope: "Tipo",
            ),
            DAConfigRowModel(
              label: "Descripción",
              scope: "Descripcion",
            ),
            DAConfigRowModel(
              label: "Observaciones",
              scope: "DescripcionExtra",
            ),
          ],
          dataSource: _dtData,
          tableActions: DALayoutDetModelTableActionsModel(
            onTap: (value) {
              currentRow = value;
            },
            onDoubleTap: (value) {
              currentRow = value;
            },
            onLongPress: (value) {
              currentRow = value;
            },
          ),
          listLongPress: [
            /* DADataTableListTile(
              icon: Icons.settings,
              title: 'Opciones de Artículo',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                if (_opcionesController
                    .articuloCoincide(currentRow['Articulo'].toString())) {
                  _opcionesController.articuloActual.reset();
                  Navigator.pushNamed(
                    context,
                    '/WidgetsForm',
                    arguments: AppDAArticulos.opciones(context, currentRow),
                  ).then(onGoBack);
                } else {
                  DAToast(context, 'Artículo sin opciones.');
                }
              },
            ), */
            DADataTableListTile(
              icon: Icons.info,
              title: 'Ver Juego',
              validation: (value) {
                return value['Tipo'] == 'Juego';
              },
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments: AppDAArticulos.juego(
                      context, articulo, articulo['Cantidad'].toString()),
                ).then(onGoBack);
              },
            ),
            DADataTableListTile(
              icon: Icons.info,
              title: 'Ver Detalle',
              onTap: (value) async {
                currentRow = value;
                Navigator.of(context).pop();
                dynamic articulo = value;
                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments:
                      AppDAArticulos.informacionNoEdit(context, articulo),
                ).then(onGoBack);
              },
            ),
            DADataTableListTile(
              icon: Icons.edit,
              title: 'Modificar',
              onTap: (value) async {
                bool _mensajeLeido = false;
                if (_pedidoActualController.pedidoActual.ofertasCalculadas) {
                  await showDialog(
                      context: context,
                      builder: ((context) {
                        return DAInputDialog(
                          title: 'Atención',
                          subtitle:
                              'Ofertas ya calculadas. Modificar un artículo reestablecerá su carrito a los precios originales. ¿Desea modificar el artículo?',
                          input: [],
                          okText: 'Sí',
                          cancelText: 'No',
                          onPressed: () async {
                            Navigator.of(context).pop();
                            _mensajeLeido = true;
                            _pedidoActualController.setOfertasCalculadas(false);
                            await totalizeOfertas(
                                carrito: _pedidoActualController
                                    .pedidoActual.carritoOriginal);
                            _pedidoActualController.resetModificado();
                            _formProv.pageRefresh();
                          },
                          onCancelPressed: () {
                            Navigator.of(context).pop();
                          },
                        );
                      }));
                }
                if (_pedidoActualController.pedidoActual.ofertasCalculadas &&
                        _mensajeLeido ||
                    !_pedidoActualController.pedidoActual.ofertasCalculadas) {
                  currentRow = value;
                  Navigator.of(context).pop();
                  dynamic articulo = value;
                  showDialog(
                    context: context,
                    builder: (context) {
                      TextEditingController _cantidadController =
                          TextEditingController();
                      return DAInputDialog(
                        title: 'Ingrese la nueva cantidad',
                        input: [
                          DAInput(
                            refID: 'nuevaCantidad',
                            controller: _cantidadController,
                            label: 'Nueva Cantidad',
                            isRequired: false,
                            disabled: false,
                            tipo: DAInputType.number,
                          ),
                        ],
                        okText: 'Confirmar',
                        cancelText: 'Cancelar',
                        onPressed: () async {
                          if (_cantidadController.text.isNotEmpty) {
                            Navigator.of(context).pop();
                            articulo['Cantidad'] = _cantidadController.text;
                            articulo['Total'] =
                                (double.parse(_cantidadController.text) *
                                        double.parse(articulo['Precio']))
                                    .toStringAsFixed(2);
                            articulo['TotalVisual'] = '\$ ${articulo['Total']}';

                            await PrefsManager.setCarritoModificar(
                                articulo: json.encode(articulo));
                            onGoBack(null);
                          }
                        },
                        onCancelPressed: () {
                          Navigator.of(context).pop();
                        },
                      );
                    },
                  );
                }
              },
            ),
            DADataTableListTile(
              icon: Icons.edit_note,
              title: 'Modificar Observaciones',
              onTap: (value) async {
                bool _mensajeLeido = false;
                if (_pedidoActualController.pedidoActual.ofertasCalculadas) {
                  await showDialog(
                      context: context,
                      builder: ((context) {
                        return DAInputDialog(
                          title: 'Atención',
                          subtitle:
                              'Ofertas ya calculadas. Modificar un artículo reestablecerá su carrito a los precios originales. ¿Desea modificar las observaciones?',
                          input: [],
                          okText: 'Sí',
                          cancelText: 'No',
                          onPressed: () async {
                            Navigator.of(context).pop();
                            _mensajeLeido = true;
                            _pedidoActualController.setOfertasCalculadas(false);
                            await totalizeOfertas(
                                carrito: _pedidoActualController
                                    .pedidoActual.carritoOriginal);
                            _pedidoActualController.resetModificado();
                            _formProv.pageRefresh();
                          },
                          onCancelPressed: () {
                            Navigator.of(context).pop();
                          },
                        );
                      }));
                }
                if (_pedidoActualController.pedidoActual.ofertasCalculadas &&
                        _mensajeLeido ||
                    !_pedidoActualController.pedidoActual.ofertasCalculadas) {
                  currentRow = value;
                  Navigator.of(context).pop();
                  dynamic articulo = value;
                  showDialog(
                    context: context,
                    builder: (context) {
                      TextEditingController _observacionesController =
                          TextEditingController(
                              text: articulo['DescripcionExtra'] ?? '');
                      return DAInputDialog(
                        title: 'Ingrese las observaciones',
                        input: [
                          DAInput(
                            refID: 'observaciones',
                            controller: _observacionesController,
                            label: 'Observaciones',
                            isRequired: false,
                            disabled: false,
                            tipo: DAInputType.textarea,
                          ),
                        ],
                        okText: 'Confirmar',
                        cancelText: 'Cancelar',
                        onPressed: () async {
                          Navigator.of(context).pop();
                          articulo['DescripcionExtra'] =
                              _observacionesController.text;

                          await PrefsManager.setCarritoModificar(
                              articulo: json.encode(articulo));
                          onGoBack(null);
                        },
                        onCancelPressed: () {
                          Navigator.of(context).pop();
                        },
                      );
                    },
                  );
                }
              },
            ),
            DADataTableListTile(
              icon: Icons.delete,
              title: 'Eliminar',
              onTap: (value) async {
                bool _mensajeLeido = false;
                if (_pedidoActualController.pedidoActual.ofertasCalculadas) {
                  await showDialog(
                      context: context,
                      builder: ((context) {
                        return DAInputDialog(
                          title: 'Atención',
                          subtitle:
                              'Ofertas ya calculadas. Eliminar un artículo reestablecerá su carrito a los precios originales. ¿Desea eliminar el artículo?',
                          input: [],
                          okText: 'Sí',
                          cancelText: 'No',
                          onPressed: () async {
                            _mensajeLeido = true;
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            currentRow = value;
                            dynamic articulo = value;
                            await PrefsManager.setCarritoEliminar(
                                articulo: json.encode(articulo));
                            _pedidoActualController.setOfertasCalculadas(false);
                            _pedidoActualController.resetModificado();
                            onGoBack(null);
                            _formProv.pageRefresh();
                          },
                          onCancelPressed: () {
                            Navigator.of(context).pop();
                          },
                        );
                      }));
                } else if (_pedidoActualController
                            .pedidoActual.ofertasCalculadas &&
                        _mensajeLeido ||
                    !_pedidoActualController.pedidoActual.ofertasCalculadas) {
                  currentRow = value;
                  Navigator.of(context).pop();
                  dynamic articulo = value;
                  await PrefsManager.setCarritoEliminar(
                      articulo: json.encode(articulo));
                  onGoBack(null);
                }
              },
            ),
          ],
        );

        List<Widget> _formBody = [
          /* TextButton(
              onPressed: () async {
                final pdf = pw.Document();

                final netLogoImage = await networkImage(
                    'https://cdn2.unrealengine.com/Diesel%2Fproduct%2Ftctd2%2Flogos%2Fgame_logo_color_1000x375-1000x375-32062fa9b2223a398be7abb362c7166d1a7d7a44.png?h=270&resize=1&w=480');

                // Función para dar formato de fecha con hora y minuto como se usa en México
                String _formatDate(DateTime date) {
                  return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
                }

                pw.Widget _header(pw.Context context) {
                  return pw.Container(
                    child: pw.Column(
                      children: [
                        // Se agrega row con logo y datos de la empresa
                        pw.Row(
                          children: [
                            pw.Container(
                              width: 200,
                              height: 200,
                              margin: pw.EdgeInsets.only(right: 25),
                              child: pw.Image(netLogoImage),
                            ),
                            pw.Container(
                              width: 300,
                              child: pw.Column(
                                children: [
                                  /* pw.Text(
                                  'Pedido',
                                  style: pw.TextStyle(
                                    fontSize: 20,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                                pw.Text(
                                  'Fecha: ${DateTime.now().toString()}',
                                  style: pw.TextStyle(
                                    fontSize: 12,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ), */
                                  pw.Text(
                                    'Muebles y Accesorios',
                                    style: pw.TextStyle(
                                      fontSize: 20,
                                      fontWeight: pw.FontWeight.bold,
                                    ),
                                  ),
                                  pw.Text(
                                    'Calle 1 # 2 - 3',
                                    style: pw.TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  pw.Text(
                                    'Teléfono: 1234567890',
                                    style: pw.TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  pw.Text(
                                    '<EMAIL>',
                                    style: pw.TextStyle(
                                      fontSize: 20,
                                      fontWeight: pw.FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        pw.Row(
                          children: [
                            pw.Container(
                              width: 200,
                              height: 50,
                              margin: pw.EdgeInsets.only(top: 25),
                              padding: pw.EdgeInsets.all(5),
                              decoration: pw.BoxDecoration(
                                borderRadius: const pw.BorderRadius.all(
                                    pw.Radius.circular(2)),
                                color: PdfColor.fromInt(0xFF0ABCDE),
                              ),
                              child: pw.GridView(
                                crossAxisCount: 2,
                                children: [
                                  pw.Text('Pedido #',
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                  pw.Text('999',
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                  pw.Text('Fecha:',
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                  pw.Text(_formatDate(DateTime.now()),
                                      style:
                                          pw.TextStyle(color: PdfColors.white)),
                                ],
                              ),
                            ),
                            pw.SizedBox(width: 50),
                          ],
                        ),
                        pw.SizedBox(height: 20),
                      ],
                    ),
                  );
                }

                pw.Widget _body(pw.Context context) {
                  const tableHeaders = [
                    'Artículo',
                    'Descripción',
                    'Precio',
                    'Cantidad',
                    'Total',
                  ];

                  return pw.Table.fromTextArray(
                    border: null,
                    cellAlignment: pw.Alignment.centerLeft,
                    headerDecoration: pw.BoxDecoration(
                      borderRadius:
                          const pw.BorderRadius.all(pw.Radius.circular(2)),
                      color: PdfColor.fromInt(0xFF0ABCDE),
                    ),
                    headerHeight: 25,
                    cellHeight: 40,
                    cellAlignments: {
                      0: pw.Alignment.centerLeft,
                      1: pw.Alignment.centerLeft,
                      2: pw.Alignment.centerRight,
                      3: pw.Alignment.center,
                      4: pw.Alignment.centerRight,
                    },
                    headerStyle: pw.TextStyle(
                      color: PdfColors.white,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    cellStyle: const pw.TextStyle(
                      color: PdfColors.black,
                      fontSize: 10,
                    ),
                    rowDecoration: pw.BoxDecoration(
                      border: pw.Border(
                        bottom: pw.BorderSide(
                          color: PdfColor.fromInt(0xFF00DDCB),
                          width: .5,
                        ),
                      ),
                    ),
                    headers: List<String>.generate(
                      tableHeaders.length,
                      (col) => tableHeaders[col],
                    ),
                    data: List<List<String>>.generate(
                      _ticketController.products.length,
                      (row) => List<String>.generate(
                        tableHeaders.length,
                        (col) => _ticketController.products[row].getIndex(col),
                      ),
                    ),
                  );
                }

                pw.Widget _footer(pw.Context context) {
                  return pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Container(
                        height: 20,
                        width: 100,
                        child: pw.BarcodeWidget(
                          barcode: pw.Barcode.pdf417(),
                          data: 'Pedido# 999',
                          drawText: false,
                        ),
                      ),
                      pw.Text(
                        'Pagina ${context.pageNumber}/${context.pagesCount}',
                        style: const pw.TextStyle(
                          fontSize: 12,
                          color: PdfColors.black,
                        ),
                      ),
                    ],
                  );
                }

                pdf.addPage(
                  pw.MultiPage(
                    pageFormat: PdfPageFormat.a4,
                    header: _header,
                    footer: _footer,
                    build: (pw.Context context) => [
                      /* _header,
                      pw.SizedBox(height: 20), */
                      _body(context),
                    ],
                  ),
                );

                await Printing.sharePdf(
                    bytes: await pdf.save(), filename: 'my-document.pdf');
              },
              child: Text('Ticket')), */
          TextButton.icon(
              style: ButtonStyle(
                alignment: Alignment.centerLeft,
                padding: WidgetStateProperty.all(
                  EdgeInsets.only(left: 16.0),
                ),
              ),
              onPressed: () async {
                String _clienteActual = await PrefsManager.getClienteActual();
                String tempClientes = await PrefsManager.getClientes();
                dynamic tempJsonClientes = json.decode(tempClientes);
                List<dynamic> clientes = tempJsonClientes['Clientes'];
                dynamic infoCLiente = clientes.firstWhere(
                  (element) {
                    return element['Cliente'] == _clienteActual;
                  },
                );

                Navigator.pushNamed(
                  context,
                  '/WidgetsForm',
                  arguments: AppDAClientes.informacionFromCarrito(
                    context,
                    infoCLiente,
                  ),
                ).then(onGoBack);
              },
              icon: Icon(Icons.info),
              label: Text('Información Cliente')),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Text(
              'Artículos: $_articulosTotales',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24.0),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 24.0),
            child: Text(
              'Total: \$ ${_total.toStringAsFixed(2)}',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24.0),
            ),
          ),
          _dtWidget,
        ];

        return _formBody;
      }

      // Descargamos datos para tabla
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        List<String>? tempCart = await PrefsManager.getCarrito();
        String _clienteActual = await PrefsManager.getClienteActual();
        _dtData.clear();

        if (_pedidoActualController.pedidoActual.ofertasCalculadas == false) {
          await totalize(carrito: tempCart);
        } else {
          await totalizeOfertas(
              carrito: _pedidoActualController.pedidoActual.carritoOferta);
        }

        if (_pedidoActualController.pedidoActual.ofertasCalculadas == false) {
          if (tempCart != null && tempCart.isNotEmpty) {
            tempCart.forEach(
              (element) {
                dynamic articulo = json.decode(element);
                if (articulo['Cliente'] == _clienteActual) {
                  _dtData.add(articulo);
                }
              },
            );
          }
        } else {
          if (_pedidoActualController.pedidoActual.carritoOferta.isNotEmpty) {
            _pedidoActualController.pedidoActual.carritoOferta.forEach(
              (articulo) {
                if (articulo['Cliente'] == _clienteActual) {
                  double importeDescuento =
                      (double.tryParse(articulo['Precio'].toString()) ?? 0.0) *
                          (double.tryParse(articulo['Cantidad'].toString()) ??
                              0.0) *
                          (double.tryParse(
                                  articulo['DescuentoLinea'].toString()) ??
                              0.0) /
                          100;
                  articulo['TotalVisual'] =
                      (double.tryParse(articulo['Precio'].toString()) ?? 0.0) *
                              (double.tryParse(
                                      articulo['Cantidad'].toString()) ??
                                  0.0) -
                          importeDescuento;
                  articulo['Tipo'] = articulo['RenglonTipo'] == 'N'
                      ? 'Normal'
                      : articulo['RenglonTipo'] == 'J'
                          ? 'Juego'
                          : articulo['RenglonTipo'] == 'L'
                              ? 'Lote'
                              : 'Componente';
                  _dtData.add(articulo);
                }
              },
            );
          }
        }

        _formBodyRes = await createWidgets();

        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();

      _modelLayoutCarrito.onRefresh = downloadMedia;
      return _modelLayoutCarrito;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }
}
