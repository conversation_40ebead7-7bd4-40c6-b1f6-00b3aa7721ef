/**************** spPM_Pedido ****************/
IF EXISTS (
    SELECT *
    FROM SYSOBJECTS
    WHERE ID = OBJECT_ID('dbo.spPM_Pedido')
)
    DROP PROCEDURE dbo.spPM_Pedido
GO

CREATE PROCEDURE dbo.spPM_Pedido
    @Usuario         VARCHAR(10),
    @Empresa         VARCHAR(5),
    @Sucursal        INT,
    @Agente          VARCHAR(10)   = NULL,
    @Almacen         VARCHAR(10)   = NULL,
    @Movimiento      VARCHAR(20)   = NULL,
    @ID              INT           = NULL,
    @Proyecto        VARCHAR(50)   = NULL,
    @Moneda          VARCHAR(10)   = NULL,
    @TipoCambio      FLOAT         = NULL,
    @ListaPrecios    VARCHAR(20)   = NULL,
    @CondicionPago   VARCHAR(50)   = NULL,
    @FechaEmision    DATETIME      = NULL,
    @Cliente         VARCHAR(10)   = NULL,
    @Referencia      VARCHAR(50)   = NULL,
    @Observaciones   VARCHAR(100)  = NULL,
    @DescuentoGlobal FLOAT         = NULL,
    @IDCita          INT           = NULL,
    @EnviarA         INT           = NULL,
    @MapaLatitud     FLOAT         = 0.0,
    @MapaLongitud    FLOAT         = 0.0
AS
BEGIN
    BEGIN TRY
        DECLARE
            @Ok            INT,
            @OkRef         VARCHAR(255) = NULL,
            @LogObs        VARCHAR(MAX) = NULL,
            @Estatus       VARCHAR(15)  = 'SINAFECTAR',
            @Concepto      VARCHAR(50)  = 'VENTAS CLIENTES',
            @UltimoCambio  DATETIME     = GETDATE();

        IF @FechaEmision IS NULL
            SELECT @FechaEmision = dbo.fnFechaSinHora(GETDATE());

        IF NOT EXISTS (
            SELECT 1
            FROM Concepto
            WHERE Modulo   = 'VTAS'
              AND Concepto = @Concepto
        )
            INSERT INTO Concepto (Modulo, Concepto)
            VALUES ('VTAS', @Concepto);

        IF NULLIF(@Almacen, '') IS NULL
            SELECT @Almacen = DefAlmacen
            FROM Usuario
            WHERE Usuario = @Usuario;

        SELECT
            @Ok = CASE
                WHEN ISNULL(@Usuario, '')        = ''  THEN 3
                WHEN ISNULL(@Empresa, '')        = ''  THEN 26070
                WHEN ISNULL(@Movimiento, '')     = ''  THEN 10062
                WHEN ISNULL(@Sucursal, -1)       = -1  THEN 10200
                WHEN ISNULL(@Moneda, '')         = ''  THEN 30040
                WHEN ISNULL(@TipoCambio, 0)      = ''  THEN 35040
                WHEN ISNULL(@Cliente, '')        = ''  THEN 40010
                WHEN ISNULL(@Almacen, '')        = ''  THEN 20390
                ELSE NULL
            END;

        IF @Ok IS NOT NULL
        BEGIN
            SELECT
                NULL   AS ID,
                Mensaje AS Ok,
                Descripcion AS OkRef
            FROM MensajeLista
            WHERE Mensaje = @Ok;
            RETURN;
        END

        IF NOT EXISTS (
            SELECT 1
            FROM UsuarioAcceso
            WHERE MovsEdicion LIKE '%' + ISNULL(@Movimiento, '') + '%'
              AND Usuario      = @Usuario
        )
        BEGIN
            SELECT
                NULL                                                            AS ID,
                71027                                                           AS Ok,
                'El Usuario no tiene permiso para el movimiento '
                + ISNULL(@Movimiento, '')                                       AS OkRef;
            RETURN;
        END

        IF @IDCita IS NOT NULL
        BEGIN
            EXEC dbo.spPM_Citas_Cambios
                @Usuario      = @Usuario,
                @Empresa      = @Empresa,
                @Agente       = @Agente,
                @Sucursal     = @Sucursal,
                @IDCita       = @IDCita,
                @MapaLatitud  = @MapaLatitud,
                @MapaLongitud = @MapaLongitud,
                @AccionMovil  = 'Confirmado',
                @SubSituacion = NULL,
                @Silencio     = 1,
                @Ok           = @Ok OUTPUT,
                @OkRef        = @OkRef OUTPUT;

            IF @Ok IS NOT NULL
            BEGIN
                SELECT @Ok   AS Ok,
                       @OkRef AS OkRef;
                RETURN;
            END
        END

        IF @ID IS NOT NULL
        BEGIN
            SET @LogObs = 'Actualizar' + ISNULL('|' + @Observaciones, '');

            UPDATE Venta
            SET
                Usuario         = @Usuario,
                Empresa         = @Empresa,
                Sucursal        = @Sucursal,
                Agente          = @Agente,
                Almacen         = @Almacen,
                Estatus         = @Estatus,
                Proyecto        = @Proyecto,
                Moneda          = @Moneda,
                TipoCambio      = @TipoCambio,
                ListaPreciosEsp = @ListaPrecios,
                FechaEmision    = @FechaEmision,
                Cliente         = @Cliente,
                Concepto        = @Concepto,
                Referencia      = @Referencia,
                Observaciones   = @Observaciones,
                DescuentoGlobal = @DescuentoGlobal,
                EnviarA         = @EnviarA,
                Condicion       = @CondicionPago
            WHERE ID  = @ID
              AND Mov = @Movimiento;

            IF @@ROWCOUNT = 0
            BEGIN
                SELECT
                    @ID    AS ID,
                    Mensaje AS Ok,
                    Descripcion AS OkRef
                FROM MensajeLista
                WHERE Mensaje = 14055;
                RETURN;
            END

            EXEC spAfectar 'VTAS', @ID, 'CONSECUTIVO', @EnSilencio = 1;
        END
        ELSE
        BEGIN
            SET @LogObs = 'Alta' + ISNULL('|' + @Observaciones, '');

            INSERT INTO Venta
            (
                Mov, Usuario, Empresa, Sucursal, Estatus, Proyecto,
                Moneda, TipoCambio, ListaPreciosEsp, FechaEmision,
                Cliente, Concepto, Agente, Referencia, Observaciones,
                Almacen, DescuentoGlobal, EnviarA, Condicion
            )
            SELECT
                @Movimiento,
                @Usuario,
                @Empresa,
                @Sucursal,
                @Estatus,
                @Proyecto,
                @Moneda,
                @TipoCambio,
                @ListaPrecios,
                @FechaEmision,
                @Cliente,
                @Concepto,
                @Agente,
                @Referencia,
                @Observaciones,
                @Almacen,
                @DescuentoGlobal,
                @EnviarA,
                @CondicionPago;

            SELECT @ID = SCOPE_IDENTITY();
            EXEC spAfectar 'VTAS', @ID, 'CONSECUTIVO', @EnSilencio = 1;
        END

        DECLARE
            @MovResultado  VARCHAR(20),
            @MovIDResultado VARCHAR(20);

        SELECT
            @MovResultado  = Mov,
            @MovIDResultado = MovID
        FROM Venta
        WHERE ID = @ID;

        EXEC spPM_LogEventosNuevo
            @Empresa      = @Empresa,
            @Usuario      = @Usuario,
            @Agente       = @Agente,
            @Proceso      = 'Pedido',
            @Referencia   = @ID,
            @MapaLatitud  = @MapaLatitud,
            @MapaLongitud = @MapaLongitud,
            @Observaciones= @LogObs,
            @Ok           = @Ok,
            @OkRef        = @OkRef;

        SELECT
            @ID             AS ID,
            @MovResultado   AS Mov,
            @MovIDResultado AS MovID,
            NULL            AS Ok,
            NULL            AS OkRef;
    END TRY
    BEGIN CATCH
        SELECT
            NULL           AS ID,
            -1             AS Ok,
            ERROR_MESSAGE() AS OkRef;
    END CATCH

    RETURN;
END
GO