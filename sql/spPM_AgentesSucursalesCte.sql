SET DATEFIRST 7
SET ANSI_NULLS OFF
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
SET LOCK_TIMEOUT -1
SET QUOTED_IDENTIFIER OFF
GO

IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.ROUTINES
          WHERE SPECIFIC_SCHEMA = N'dbo'
            AND SPECIFIC_NAME = N'spPM_AgentesSucursalesCte'
            AND ROUTINE_TYPE = N'PROCEDURE')
    DROP PROCEDURE dbo.spPM_AgentesSucursalesCte
GO

CREATE PROCEDURE spPM_AgentesSucursalesCte @Agente VARCHAR(10)
AS
BEGIN
    SELECT Cliente                 AS Cliente,
           ID                      AS ID,
           Nombre                  AS Nombre,
           Direccion               AS Direccion,
           DireccionNumero         AS DireccionNumero,
           DireccionNumeroInt      AS DireccionNumeroInt,
           EntreCalles             AS EntreCalles,
           Plano                   AS Plano,
           Observaciones           AS Observaciones,
           Colonia                 AS Colonia,
           Delegacion              AS Delegacion,
           Poblacion               AS Poblacion,
           Estado                  AS Estado,
           Pais                    AS Pais,
           Zona                    AS Zona,
           Ruta                    AS Ruta,
           CodigoPostal            AS CodigoPostal,
           Telefonos               AS Telefonos,
           Fax                     AS Fax,
           PedirTono               AS PedirTono,
           Contacto1               AS Contacto1,
           Contacto2               AS Contacto2,
           Extencion1              AS Extencion1,
           Extencion2              AS Extencion2,
           eMail1                  AS eMail1,
           eMail2                  AS eMail2,
           eMailAuto               AS eMailAuto,
           ZonaImpuesto            AS ZonaImpuesto,
           Agente                  AS Agente,
           Clase                   AS Clase,
           Estatus                 AS Estatus,
           UltimoCambio            AS UltimoCambio,
           Alta                    AS Alta,
           Mensaje                 AS Mensaje,
           FormaEnvio              AS FormaEnvio,
           ListaPreciosEsp         AS ListaPreciosEsp,
           Proyecto                AS Proyecto,
           Condicion               AS Condicion,
           Descuento               AS Descuento,
           ModificarVencimiento    AS ModificarVencimiento,
           Categoria               AS Categoria,
           Grupo                   AS Grupo,
           Familia                 AS Familia,
           TieneMovimientos        AS TieneMovimientos,
           Contrasena              AS Contrasena,
           Clave                   AS Clave,
           SucursalEmpresa         AS SucursalEmpresa,
           PersonalCobrador        AS PersonalCobrador,
           Almacen                 AS Almacen,
           AlmacenVtasConsignacion AS AlmacenVtasConsignacion,
           wMovVentas              AS wMovVentas,
           Unidad                  AS Unidad,
           FiscalRegimen           AS FiscalRegimen,
           FiscalZona              AS FiscalZona
    FROM CteEnviarA
    WHERE Estatus = 'ALTA'
      AND Agente = @Agente
END
GO
