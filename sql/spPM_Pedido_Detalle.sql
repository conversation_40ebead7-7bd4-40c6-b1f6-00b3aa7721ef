SET DATEFIRST 7
SET ANSI_NULLS OFF
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
SET LOCK_TIMEOUT -1
SET QUOTED_IDENTIFIER OFF
GO

/**************** spPM_Pedido_Detalle ****************/
IF EXISTS(SELECT *
FROM SYSOBJECTS
WHERE ID = OBJECT_ID('dbo.spPM_Pedido_Detalle'))
    DROP PROCEDURE dbo.spPM_Pedido_Detalle
GO
CREATE PROC spPM_Pedido_Detalle
    @Usuario varchar(10) = NULL,
    @Estacion int = NULL,
    @ID int = NULL,
    @Renglon int = NULL,
    @RenglonTipo varchar(1) = NULL,
    @Ultimo bit = 0,
    @Articulo varchar(20) = NULL,
    @SubCuenta varchar(20) = NULL,
    @Cantidad float = NULL,
    @Precio float = NULL,
    @Unidad varchar(50) = NULL,
    @DescuentoLinea float = NULL,
    @DescuentoImporte float = NULL,
    @Impuesto1 float = NULL,
    @Impuesto2 float = NULL,
    @Impuesto3 float = NULL,
    @Retencion1 float = NULL,
    @Retencion2 float = NULL,
    @Retencion3 float = NULL,
    @DescripcionExtra varchar(100) = NULL

--//WITH ENCRYPTION
AS
BEGIN
    BEGIN TRY
        DECLARE
            @Empresa varchar(5),
            @Estatus varchar(15),
            @Almacen varchar(10),
            @Importe float,
            @Impuestos float,
            @Factor float,
            @IDOport int,
            @Movimiento varchar(50),
            @Propuesta varchar(50),
            @RenglonID int,
            @Agente varchar(10),
            @Sucursal int,
            @Cliente varchar(50),
            @Moneda varchar(10),
            @TipoCambio FLOAT,
            @Ok int,
            @OkRef varchar(255)

        IF @Renglon IS NULL SELECT @Renglon = MAX(Renglon) + 1024
    FROM VentaD
    WHERE ID = @ID
        IF @Renglon IS NULL SELECT @Renglon = 1024

        SELECT @Estatus = Estatus,
        @Empresa = Empresa,
        @Almacen = Almacen,
        @Agente = Agente,
        @Sucursal = Sucursal,
        @Cliente = Cliente
    FROM Venta
    WHERE ID = @ID
        SELECT @Articulo = Articulo
    FROM Art
    WHERE Articulo = @Articulo

        SELECT @Moneda = LP.Moneda
    FROM Agente Ag
        JOIN Cte Ct ON Ag.Agente = Ct.Agente AND Ag.Agente = @Agente
        LEFT JOIN ListaPreciosD LP ON Ct.ListaPreciosEsp = LP.Lista
    WHERE ct.Cliente = @Cliente

        IF @Moneda IS NULL
            SELECT TOP 1
        @Moneda = MonedaBase
    FROM MovilUsuarioCfg

        SELECT @TipoCambio = TipoCambio
    FROM Mon
    WHERE Moneda = @Moneda

        IF NOT EXISTS(SELECT Articulo
    FROM Art
    WHERE Articulo = @Articulo)
            SET @Articulo = NULL

        IF ISNULL(@Unidad, '') = ''
            SELECT @Unidad = Unidad
    FROM Art
    WHERE Articulo = @Articulo

        -- Validaciones
        SELECT @Ok = CASE
                         WHEN ISNULL(@Estatus, '') NOT IN ('SINAFECTAR', 'CONFIRMAR') THEN 10045
                         WHEN ISNULL(@Articulo, '') = '' THEN 10530
                         WHEN @Cantidad IS NULL THEN 20015
                         WHEN @Precio IS NULL THEN 20305
                         WHEN ISNULL(@Unidad, '') = '' THEN 20150
                         ELSE NULL END

        IF (@Ok IS NOT NULL)
            BEGIN
        SELECT @ID ID, @Renglon Renglon, Mensaje Ok, Descripcion OkRef
        FROM MensajeLista
        WHERE Mensaje = @Ok
        RETURN
    END

        SELECT @Factor = ISNULL(dbo.fnArtUnidadFactor(@Empresa, @Articulo, @Unidad), 1)

        IF EXISTS(SELECT ID
    FROM VentaD
    WHERE ID = @ID AND Renglon = @Renglon)
            BEGIN
        UPDATE VentaD
                SET Articulo           = @Articulo,
                    SubCuenta          = @SubCuenta,
                    Cantidad           = @Cantidad,
                    Precio             = @Precio,
                    Unidad             = @Unidad,
                    DescuentoLinea     = @DescuentoLinea,
                    Impuesto1          = @Impuesto1,
                    Impuesto2          = @Impuesto2,
                    Impuesto3          = @Impuesto3,
                    Retencion1         = @Retencion1,
                    Retencion2         = @Retencion2,
                    Retencion3         = @Retencion3,
                    Factor             = @Factor,
                    CantidadInventario = @Cantidad * @Factor,
                    Almacen            = @Almacen,
                    TipoImpuesto1      = @Impuesto1,
                    DescripcionExtra   = @DescripcionExtra
                WHERE ID = @ID
            AND Renglon = @Renglon

        IF @@ROWCOUNT = 0
                    SELECT @Ok = Mensaje, @OkRef = Descripcion
        FROM MensajeLista
        WHERE Mensaje = 10066
    END
        ELSE
            BEGIN
        IF @RenglonTipo = 'C'
                    BEGIN
            SELECT @RenglonID = MAX(RenglonID)
            FROM VentaD
            WHERE ID = @ID
        END
                ELSE
                    BEGIN
            SELECT @RenglonID = MAX(RenglonID) + 1
            FROM VentaD
            WHERE ID = @ID
        END
        IF @RenglonID IS NULL SET @RenglonID = 1

        IF @RenglonTipo IS NULL
                    BEGIN
            EXEC spRenglonTipo @Articulo, @SubCuenta, @RenglonTipo OUTPUT
        END

        INSERT INTO VentaD
            (ID, Renglon, RenglonID, RenglonSub, RenglonTipo, Articulo, SubCuenta, Cantidad,
            Precio, Factor, DescuentoLinea, Impuesto1, Impuesto2, Impuesto3, Almacen, Unidad,
            PrecioSugerido, DescuentoImporte, CantidadInventario, Agente, Sucursal,
            SucursalOrigen, PrecioTipoCambio, PrecioMoneda,
            Retencion1, Retencion2, Retencion3, TipoImpuesto1, DescripcionExtra)
        SELECT @ID,
            @Renglon,
            @RenglonID,
            0,
            @RenglonTipo,
            @Articulo,
            @SubCuenta,
            @Cantidad,
            @Precio,
            @Factor,
            @DescuentoLinea,
            @Impuesto1,
            @Impuesto2,
            @Impuesto3,
            @Almacen,
            @Unidad,
            @Precio,
            @DescuentoImporte,
            @Cantidad * @Factor,
            @Agente,
            @Sucursal,
            @Sucursal,
            @TipoCambio,
            @Moneda,
            @Retencion1,
            @Retencion2,
            @Retencion3,
            @Impuesto1,
            @DescripcionExtra

        DECLARE @ImporteD float, @ImpuestosD float;
        SELECT @ImporteD = SUM(SubTotal), @ImpuestosD = SUM(Impuestos)
        FROM VentaTCalc
        WHERE ID = @ID

        UPDATE Venta 
        SET Importe = @ImporteD,
            Impuestos = @ImpuestosD
        WHERE ID = @ID

        IF @@ROWCOUNT = 0
                    SELECT @Ok = Mensaje, @OkRef = Descripcion
        FROM MensajeLista
        WHERE Mensaje = 10066
    END

        -- Pedidos Movil no Afecta
        --IF(@Ultimo = 1)
        --BEGIN
        --	-- Afectamos Pedido
        --	IF (@Ok IS NULL)
        --		EXEC spAfectar 'VTAS', @ID, 'AFECTAR', 'Todo', NULL, @Usuario, @EnSilencio = 1,  @Ok = @Ok output, @OkRef = @OkRef output, @Estacion = @Estacion
        --END

        IF (@Ok IS NOT NULL)
            BEGIN
        SELECT @ID ID, @Renglon Renglon, @Ok Ok, @OkRef OkRef
        RETURN
    END

        SELECT @Renglon Renglon, NULL Ok, NULL OkRef
        RETURN
    END TRY
    BEGIN CATCH
        SELECT @Renglon Renglon, -1 Ok, ERROR_MESSAGE() OkRef
    FROM MensajeLista
    WHERE Mensaje = 10066
        RETURN
    END CATCH

    RETURN
END
GO