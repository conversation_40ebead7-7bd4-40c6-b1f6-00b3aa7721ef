SET DATEFIRST 7
SET ANSI_NULLS OFF
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
SET LOCK_TIMEOUT -1
SET QUOTED_IDENTIFIER OFF

GO

IF EXISTS (SELECT * FROM sysobjects WHERE id = OBJECT_ID('dbo.spWebPrecioPoliticaDipath1') AND type = 'P') DROP PROCEDURE dbo.spWebPrecioPoliticaDipath1
GO

CREATE PROCEDURE spWebPrecioPoliticaDipath1
    @Cliente            VARCHAR(10),
    @Articulo           VARCHAR(20),
    @Cantidad           FLOAT,
    @PrecioInicial      FLOAT,
    @SucursalMov        INT,
    @SucursalAgente     INT = NULL,
    @Precio             FLOAT OUTPUT,
    --No se ocupan
    @DescuentoTipo      VARCHAR(23) OUTPUT,
    @Descuento          FLOAT OUTPUT,
    @DescuentoMonto     FLOAT OUTPUT

WITH ENCRYPTION
AS
BEGIN

    DECLARE
        @Categoria          VARCHAR(50),
        @Region             VARCHAR(50),
        @Politica           INT,
        @PoliticaxArt       INT,
        @PoliticaxFam       INT,
        @PoliticaxGrupo     INT,
        @Monto              FLOAT,
        @ArtFam             VARCHAR(50),
        @ArtGrupo           VARCHAR(50),
        @FormaEnvio         VARCHAR(50)

    SELECT  @Categoria = UPPER(Categoria),
            @ArtFam = Familia,
            @ArtGrupo = Grupo
    FROM ART WHERE Articulo = @Articulo

    SELECT @FormaEnvio = FormaEnvio FROM Cte WHERE Cliente = @Cliente

    SELECT @Region = Region FROM Sucursal WHERE Sucursal = @SucursalMov

    IF @Categoria = 'LISTA'
    BEGIN
        SELECT @Monto = CONVERT(FLOAT, REPLACE(Grupo, '%', '')) FROM Cte WHERE Cliente = @Cliente
        SELECT @DescuentoTipo = 'Cliente', @Descuento = @Monto, @DescuentoMonto = (@PrecioInicial - dbo.fnDisminuyePorcentaje(@PrecioInicial, @Monto))
        SELECT @Precio = dbo.fnDisminuyePorcentaje(@PrecioInicial, @Monto)
    END

    IF @Categoria = 'PROMOCION'
    BEGIN
        IF @SucursalAgente IS NOT NULL
        BEGIN
            -- PRIMER INTENTO: Búsqueda con SucursalAgente específica
            SELECT TOP(1) @PoliticaxArt = Id, @DescuentoTipo = Tipo
            FROM PoliticaPrecios
            WHERE 1=1
            AND Articulo = @Articulo
            AND Region = @Region
            AND Nivel = 'Promocion'
            AND Tipo IN ('Precio','% Descuento')
            AND ISNULL(FormaEnvio,'') = ISNULL(@FormaEnvio,'')
            AND Sucursal = @SucursalAgente
            ORDER BY ID DESC

            IF ISNULL(@ArtFam,'') <> ''
            BEGIN
                SELECT TOP(1) @PoliticaxFam = Id, @DescuentoTipo = Tipo
                FROM PoliticaPrecios
                WHERE 1=1
                AND ArtFam = @ArtFam
                AND Region = @Region
                AND Nivel = 'Promocion'
                AND Tipo IN ('Precio','% Descuento')
                AND ISNULL(FormaEnvio,'') = ISNULL(@FormaEnvio,'')
                AND Sucursal = @SucursalAgente
                ORDER BY ID DESC
            END

            IF ISNULL(@ArtGrupo,'') <> ''
            BEGIN
                SELECT TOP(1) @PoliticaxGrupo = Id, @DescuentoTipo = Tipo
                FROM PoliticaPrecios
                WHERE 1=1
                AND ArtGrupo = @ArtGrupo
                AND Region = @Region
                AND Nivel = 'Promocion'
                AND Tipo IN ('Precio','% Descuento')
                AND ISNULL(FormaEnvio,'') = ISNULL(@FormaEnvio,'')
                AND Sucursal = @SucursalAgente
                ORDER BY ID DESC
            END

            SELECT @Politica = COALESCE(@PoliticaxArt, @PoliticaxFam, @PoliticaxGrupo)
        END

        -- SEGUNDO INTENTO: Si no se encontró política o @SucursalAgente es NULL, búsqueda con la FormaEnvio específica del cliente
        IF @Politica IS NULL
        BEGIN
            SET @PoliticaxArt = NULL
            SET @PoliticaxFam = NULL
            SET @PoliticaxGrupo = NULL
            SET @DescuentoTipo = NULL

            SELECT TOP(1) @PoliticaxArt = Id, @DescuentoTipo = Tipo
            FROM PoliticaPrecios
            WHERE 1=1
            AND Articulo = @Articulo
            AND Region = @Region
            AND Nivel = 'Promocion'
            AND Tipo IN ('Precio','% Descuento')
            AND ISNULL(FormaEnvio,'') = ISNULL(@FormaEnvio,'')
            ORDER BY ID DESC

            IF ISNULL(@ArtFam,'') <> ''
            BEGIN
                SELECT TOP(1) @PoliticaxFam = Id, @DescuentoTipo = Tipo
                FROM PoliticaPrecios
                WHERE 1=1
                AND ArtFam = @ArtFam
                AND Region = @Region
                AND Nivel = 'Promocion'
                AND Tipo IN ('Precio','% Descuento')
                AND ISNULL(FormaEnvio,'') = ISNULL(@FormaEnvio,'')
                ORDER BY ID DESC
            END

            IF ISNULL(@ArtGrupo,'') <> ''
            BEGIN
                SELECT TOP(1) @PoliticaxGrupo = Id, @DescuentoTipo = Tipo
                FROM PoliticaPrecios
                WHERE 1=1
                AND ArtGrupo = @ArtGrupo
                AND Region = @Region
                AND Nivel = 'Promocion'
                AND Tipo IN ('Precio','% Descuento')
                AND ISNULL(FormaEnvio,'') = ISNULL(@FormaEnvio,'')
                ORDER BY ID DESC
            END

            SELECT @Politica = COALESCE(@PoliticaxArt, @PoliticaxFam, @PoliticaxGrupo)
        END

        -- TERCER INTENTO: Si no se encontró política, buscar sin FormaEnvio específica (NULL o '')
        IF @Politica IS NULL
        BEGIN
            SET @PoliticaxArt = NULL
            SET @PoliticaxFam = NULL
            SET @PoliticaxGrupo = NULL
            SET @DescuentoTipo = NULL

            SELECT TOP(1) @PoliticaxArt = Id, @DescuentoTipo = Tipo
            FROM PoliticaPrecios
            WHERE 1=1
            AND Articulo = @Articulo
            AND Region = @Region
            AND Nivel = 'Promocion'
            AND Tipo IN ('Precio','% Descuento')
            AND ISNULL(FormaEnvio,'') = ''
            ORDER BY ID DESC

            IF ISNULL(@ArtFam,'') <> ''
            BEGIN
                SELECT TOP(1) @PoliticaxFam = Id, @DescuentoTipo = Tipo
                FROM PoliticaPrecios
                WHERE 1=1
                AND ArtFam = @ArtFam
                AND Region = @Region
                AND Nivel = 'Promocion'
                AND Tipo IN ('Precio','% Descuento')
                AND ISNULL(FormaEnvio,'') = ''
                ORDER BY ID DESC
            END

            IF ISNULL(@ArtGrupo,'') <> ''
            BEGIN
                SELECT TOP(1) @PoliticaxGrupo = Id, @DescuentoTipo = Tipo
                FROM PoliticaPrecios
                WHERE 1=1
                AND ArtGrupo = @ArtGrupo
                AND Region = @Region
                AND Nivel = 'Promocion'
                AND Tipo IN ('Precio','% Descuento')
                AND ISNULL(FormaEnvio,'') = ''
                ORDER BY ID DESC
            END

            SELECT @Politica = COALESCE(@PoliticaxArt, @PoliticaxFam, @PoliticaxGrupo)
        END -- Fin del tercer intento

        IF (@Politica IS NOT NULL) AND (@Cantidad > 0)
        BEGIN

            DECLARE
                @PoliticaD TABLE
                (
                    Renglon INT NOT NULL IDENTITY (1, 1),
                    Idpolitica INT NULL,
                    CantidadD FLOAT NULL,
                    CantidadA FLOAT NULL,
                    MontoP  FLOAT NULL
                )

            DECLARE @ValorMaximo FLOAT, @ValorMinimo FLOAT

            SELECT TOP(1) @ValorMaximo = Cantidad FROM PoliticaPreciosD WHERE ID = @Politica ORDER BY Cantidad DESC
            SELECT TOP(1) @ValorMinimo = Cantidad FROM PoliticaPreciosD WHERE ID = @Politica ORDER BY Cantidad ASC

            INSERT INTO @PoliticaD (Idpolitica, CantidadD, MontoP)
            SELECT @Politica, Cantidad, Monto FROM PoliticaPreciosD WHERE ID = @Politica ORDER BY Cantidad ASC

            DECLARE
                @i INT,
                @j INT

            SET @i = 0
            SET @j = 1

            SET @i = (SELECT COUNT(Renglon) FROM @PoliticaD)

            WHILE @j <= @i
            BEGIN
                IF EXISTS(SELECT Renglon FROM @PoliticaD WHERE Renglon = @j + 1)
                BEGIN
                    UPDATE @PoliticaD SET CantidadA = (SELECT (CantidadD - 0.000001) FROM @PoliticaD WHERE Renglon = (@j+1))
                    WHERE Renglon = @j
                END
                ELSE
                BEGIN
                    UPDATE @PoliticaD SET CantidadA = @ValorMaximo
                    WHERE Renglon = @j
                END
                SET @j = @j + 1
            END

            IF @Cantidad > @ValorMaximo
            BEGIN
                SELECT TOP (1) @Precio = MontoP FROM @PoliticaD ORDER BY CantidadD DESC
            END
            ELSE
            BEGIN
                SELECT @Precio = MontoP, @Descuento = @Monto, @DescuentoMonto = (@PrecioInicial - MontoP)
                FROM @PoliticaD
                WHERE Idpolitica = @Politica
                AND CantidadD <= @Cantidad
                AND CantidadA >= @Cantidad
            END

            IF @Cantidad < @ValorMinimo
            BEGIN
                SELECT @Precio = @PrecioInicial
            END
        END
        ELSE
        BEGIN
            SELECT @Precio = @PrecioInicial
        END
    END

    IF (@Categoria <> 'LISTA') AND (@Categoria <> 'PROMOCION')
        SELECT @Precio = @PrecioInicial
        SELECT @DescuentoTipo = '', @DescuentoMonto = 0, @Descuento = 0

END
GO
