SET DATEFIRST 7
SET ANSI_NULLS OFF
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
SET LOCK_TIMEOUT -1
SET QUOTED_IDENTIFIER OFF
GO

IF EXISTS (SELECT *
           FROM sysobjects
           WHERE id = object_id('spPM_Clientes')
             and type = 'P')
    DROP PROCEDURE spPM_Clientes
GO

CREATE PROC spPM_Clientes @Usuario varchar(10),
                          @Empresa varchar(5),
                          @Agente varchar(10),
                          @TodosCtes bit = 0,
                          @Cliente varchar(10) = NULL
AS
BEGIN

    DECLARE @Limite TABLE
                    (
                        LimiteCreditoMN money NULL
                    )

    DECLARE
        @Saldo money,
        @Cliente2 varchar(10),
        @LimiteCreditoMN money

    DECLARE @ClienteT TABLE
                      (
                          Cliente          varchar(10) NULL,
                          Saldo            money       NULL,
                          LimiteCreditoMN  money       NULL,
                          CreditoDiponible money       NULL
                      )

    INSERT INTO @ClienteT
        (Cliente)
    SELECT DISTINCT C.Cliente
    FROM Cte C
             LEFT JOIN CteEnviarA C2
                       ON C.Cliente = C2.Cliente
                           AND (C2.Nombre NOT LIKE '%Carta%' OR C2.Nombre NOT LIKE '%Porte%')
                           AND C2.Agente = @Agente
    WHERE C.Estatus = 'ALTA'
      AND C.Agente = IIF(ISNULL(@TodosCtes, 0) = 0, @Agente, C.Agente)
      AND C.Cliente = ISNULL(@Cliente, C.Cliente)

    SELECT @Cliente2 = MIN(Cliente)
    FROM @ClienteT
    WHILE @Cliente2 IS NOT NULL
        BEGIN
            SELECT @Saldo = 0.00, @LimiteCreditoMN = 0.00

            SELECT @Saldo = SUM(s.Saldo * m.TipoCambio)
            FROM CxcSaldo s
                     JOIN Mon m on s.Moneda = m.Moneda
            WHERE Empresa = @Empresa
              AND Cliente = @Cliente2

            INSERT INTO @Limite
                EXEC spVerLimiteCreditoMN @Cliente2, @Empresa
            SELECT @LimiteCreditoMN = LimiteCreditoMN
            from @Limite

            UPDATE @ClienteT
            SET Saldo           = ISNULL(@Saldo, 0.00),
                LimiteCreditoMN = @LimiteCreditoMN
            WHERE Cliente = @Cliente2
            DELETE @Limite
            SELECT @Cliente2 = MIN(Cliente)
            FROM @ClienteT
            WHERE Cliente > @Cliente2
        END

    SELECT DISTINCT ISNULL(a.Cliente, '')                     AS Cliente,
                    COALESCE(a.Agente, e.Agente, '')          AS Agente,
                    ISNULL(a.Nombre, '')                      AS Nombre,
                    ISNULL(a.NombreCorto, '')                 AS NombreCorto,
                    ISNULL(a.Direccion, '')                   AS Direccion,
                    ISNULL(a.DireccionNumero, '')             AS DireccionNumero,
                    ISNULL(a.DireccionNumeroInt, '')          AS DireccionNumeroInt,
                    ISNULL(a.EntreCalles, '')                 AS EntreCalles,
                    ISNULL(a.Delegacion, '')                  AS Delegacion,
                    ISNULL(a.Colonia, '')                     AS Colonia,
                    ISNULL(a.Poblacion, '')                   AS Poblacion,
                    ISNULL(a.Estado, '')                      AS Estado,
                    ISNULL(a.Pais, '')                        AS Pais,
                    ISNULL(a.CodigoPostal, '')                AS CodigoPostal,
                    ISNULL(a.RFC, '')                         AS RFC,
                    ISNULL(a.Telefonos, '')                   AS Telefonos,
                    ISNULL(a.TelefonosLada, '')               AS TelefonosLada,
                    ISNULL(a.ListaPrecios, '')                AS ListaPrecios,
                    ISNULL(a.ListaPreciosEsp, '')             AS ListaPreciosEsp,
                    ISNULL(a.DefMoneda, '')                   AS DefMoneda,
                    ISNULL(a.MapaUbicacion, '')               AS MapaUbicacion,
                    ISNULL(a.MapaLatitud, '')                 AS MapaLatitud,
                    ISNULL(a.MapaLongitud, '')                AS MapaLongitud,
                    ISNULL(a.MapaPrecision, '')               AS MapaPrecision,
                    ISNULL(a.AlmacenDef, '')                  AS AlmacenDef,
                    ISNULL(a.Grupo, '')                       AS Grupo,
                    ISNULL(a.Familia, '')                     AS Familia,
                    ISNULL(a.Categoria, '')                   AS Categoria,
                    ISNULL(a.Contacto1, '')                   AS Contacto1,
                    ISNULL(a.Contacto2, '')                   AS Contacto2,
                    ISNULL(a.eMail1, '')                      AS eMail1,
                    ISNULL(a.eMail2, '')                      AS eMail2,
                    ISNULL(a.CreditoLimite, '')               AS CreditoLimite,
                    ISNULL(a.Descuento, '')                   AS Descuento,
                    ISNULL(a.EnviarA, '')                     AS EnviarA,
                    ISNULL(a.Estatus, '')                     AS Estatus,
                    ISNULL(e.ID, '')                          AS Sucursal,
                    ISNULL(e.Nombre, '')                      AS SucursalNombre,
                    ISNULL(a.Estatus, '')                     AS Estatus,
                    ISNULL(b.Saldo, '')                       AS Saldo,
                    ISNULL(b.LimiteCreditoMN, '')             AS LimiteCreditoMN,
                    ISNULL((b.LimiteCreditoMN - b.Saldo), '') AS DisponibleMN,
                    ISNULL(a.Condicion, '')                   AS Condicion,
                    ISNULL(c.eMail, '')                       AS eMailCto,
                    ISNULL(a.SucursalEmpresa, '')             AS SucursalEmpresa,
                    ISNULL(a.ZonaImpuesto, '')                AS ZonaImpuesto
    FROM Cte a
             JOIN @ClienteT b ON a.Cliente = b.Cliente
             LEFT JOIN CteEnviarA e
                       ON a.Cliente = e.Cliente
                           AND (e.Nombre NOT LIKE '%Carta%' OR e.Nombre NOT LIKE '%Porte%')
                           AND e.Agente = @Agente
             LEFT JOIN (SELECT Cliente, eMail, ROW_NUMBER() OVER (PARTITION BY Cliente ORDER BY (SELECT NULL)) AS RowNum
                        FROM CteCto
                        WHERE Nombre = 'Pedidos Móviles') c ON a.Cliente = c.Cliente AND c.RowNum = 1

    SELECT ISNULL(Cxc.ID, '')           AS ID,
           ISNULL(Cxc.Empresa, '')      AS Empresa,
           ISNULL(Cxc.Cliente, '')      AS Cliente,
           ISNULL(Cxc.Mov, '')          AS Mov,
           ISNULL(Cxc.MovID, '')        AS MovID,
           ISNULL(Cxc.FechaEmision, '') AS FechaEmision,
           ISNULL(Cxc.Saldo, '')        AS Saldo,
           ISNULL(Cxc.Situacion, '')    AS Situacion,
           ISNULL(Cxc.Moneda, '')       AS Moneda,
           ISNULL(Cxc.TipoCambio, '')   AS TipoCambio,
           ISNULL(Cxc.Sucursal, '')     AS Sucursal
    FROM vwPM_CxcInfo313 Cxc
             INNER JOIN @ClienteT c on c.Cliente = Cxc.Cliente
    WHERE Cxc.Empresa = @Empresa
    RETURN
END
GO