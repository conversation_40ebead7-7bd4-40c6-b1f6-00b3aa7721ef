SET DATEFIRST 7
SET ANSI_NULLS OFF
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
SET LOCK_TIMEOUT -1
SET QUOTED_IDENTIFIER OFF

GO

IF EXISTS (
    SELECT * FROM sysobjects
    WHERE id = OBJECT_ID('dbo.spWebPrecioPoliticaDipath1')
      AND type = 'P'
)
    DROP PROCEDURE dbo.spWebPrecioPoliticaDipath1
GO

CREATE PROCEDURE dbo.spWebPrecioPoliticaDipath1
    @Cliente         varchar(10),
    @Articulo        varchar(20),
    @Cantidad        float, 
    @PrecioInicial   float,
    @SucursalMov     int,
    @SucursalAgente  int = NULL,
    @Precio          float OUTPUT,
    @DescuentoTipo   varchar(23) OUTPUT,
    @Descuento       float OUTPUT,
    @DescuentoMonto  float OUTPUT
WITH ENCRYPTION
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE
        @Categoria    varchar(50),
        @ArtFam       varchar(50),
        @ArtGrupo     varchar(50),
        @FormaEnvio   varchar(50),
        @Region       varchar(50);

    -- 1) Datos básicos
    SELECT
        @Categoria = UPPER(Categoria),
        @ArtFam    = Familia,
        @ArtGrupo  = Grupo
    FROM ART
    WHERE Articulo = @Articulo;

    SELECT @FormaEnvio = FormaEnvio
    FROM Cte
    WHERE Cliente = @Cliente;

    SELECT @Region = Region
    FROM Sucursal
    WHERE Sucursal = @SucursalMov;

    -- Si no es PROMOCION, lógica original para LISTA u otros
    IF @Categoria = 'LISTA'
    BEGIN
        DECLARE @Monto float;
        SELECT @Monto = CONVERT(float, REPLACE(Grupo, '%', ''))
        FROM Cte
        WHERE Cliente = @Cliente;

        SET @DescuentoTipo  = 'Cliente';
        SET @Descuento      = @Monto;
        SET @DescuentoMonto = (@PrecioInicial - dbo.fnDisminuyePorcentaje(@PrecioInicial, @Monto));
        SET @Precio         = dbo.fnDisminuyePorcentaje(@PrecioInicial, @Monto);
        RETURN;
    END

    IF @Categoria <> 'PROMOCION'
    BEGIN
        SET @Precio         = @PrecioInicial;
        SET @DescuentoTipo  = '';
        SET @Descuento      = 0;
        SET @DescuentoMonto = 0;
        RETURN;
    END

    -- 2) Buscamos id y tipo de política POR REGIÓN
    DECLARE
        @PolReg_Id   int = NULL,
        @PolReg_Tipo varchar(23) = NULL;

    SELECT TOP(1)
        @PolReg_Id   = PP.ID,
        @PolReg_Tipo = PP.Tipo
    FROM PoliticaPrecios PP
    WHERE
        PP.Nivel    = 'Promocion'
        AND PP.Tipo IN ('Precio','% Descuento')
        AND PP.Region = @Region
        AND (
            PP.Articulo = @Articulo
            OR (ISNULL(@ArtFam, '') <> '' AND PP.ArtFam = @ArtFam)
            OR (ISNULL(@ArtGrupo, '') <> '' AND PP.ArtGrupo = @ArtGrupo)
        )
    ORDER BY
        CASE
            WHEN PP.Articulo = @Articulo THEN 1
            WHEN PP.ArtFam    = @ArtFam    THEN 2
            WHEN PP.ArtGrupo  = @ArtGrupo  THEN 3
            ELSE 4
        END,
        PP.ID DESC;

    -- 3) Buscamos id y tipo de política POR SUCURSAL (FormaEnvío + SucursalAgente)
    DECLARE
        @PolSuc_Id   int = NULL,
        @PolSuc_Tipo varchar(23) = NULL;

    SELECT TOP(1)
        @PolSuc_Id   = PP.ID,
        @PolSuc_Tipo = PP.Tipo
    FROM PoliticaPrecios PP
    WHERE
        PP.Nivel    = 'Promocion'
        AND PP.Tipo IN ('Precio','% Descuento')
        AND ISNULL(PP.FormaEnvio, '') = ISNULL(@FormaEnvio, '')
        AND PP.Sucursal = @SucursalAgente
        AND (
            PP.Articulo = @Articulo
            OR (ISNULL(@ArtFam, '') <> '' AND PP.ArtFam = @ArtFam)
            OR (ISNULL(@ArtGrupo, '') <> '' AND PP.ArtGrupo = @ArtGrupo)
        )
    ORDER BY
        CASE
            WHEN PP.Articulo = @Articulo THEN 1
            WHEN PP.ArtFam    = @ArtFam    THEN 2
            WHEN PP.ArtGrupo  = @ArtGrupo  THEN 3
            ELSE 4
        END,
        PP.ID DESC;

    -- 4) Aplicar lógica de cantidades a cada política encontrada
    DECLARE
        @PrecioReg       float = NULL,
        @DesctoRegTipo   varchar(23) = NULL,
        @DesctoReg       float = NULL,
        @DesctoRegMonto  float = NULL,
        @PrecioSuc       float = NULL,
        @DesctoSucTipo   varchar(23) = NULL,
        @DesctoSuc       float = NULL,
        @DesctoSucMonto  float = NULL;

    -- Por Región
    IF @PolReg_Id IS NOT NULL
    BEGIN
        SELECT TOP(1)
            @PrecioReg = PD.Monto
        FROM PoliticaPreciosD PD
        WHERE PD.ID = @PolReg_Id
          AND PD.Cantidad <= @Cantidad
        ORDER BY PD.Cantidad DESC;

        IF @PrecioReg IS NULL
            SET @PrecioReg = @PrecioInicial;

        SET @DesctoRegTipo  = @PolReg_Tipo;
        SET @DesctoRegMonto = @PrecioInicial - @PrecioReg;
        IF @DesctoRegTipo = '% Descuento'
            SET @DesctoReg = ROUND(100.0 * @DesctoRegMonto / @PrecioInicial, 4);
        ELSE
            SET @DesctoReg = 0;
    END

    -- Por Sucursal
    IF @PolSuc_Id IS NOT NULL
    BEGIN
        SELECT TOP(1)
            @PrecioSuc = PD.Monto
        FROM PoliticaPreciosD PD
        WHERE PD.ID = @PolSuc_Id
          AND PD.Cantidad <= @Cantidad
        ORDER BY PD.Cantidad DESC;

        IF @PrecioSuc IS NULL
            SET @PrecioSuc = @PrecioInicial;

        SET @DesctoSucTipo  = @PolSuc_Tipo;
        SET @DesctoSucMonto = @PrecioInicial - @PrecioSuc;
        IF @DesctoSucTipo = '% Descuento'
            SET @DesctoSuc = ROUND(100.0 * @DesctoSucMonto / @PrecioInicial, 4);
        ELSE
            SET @DesctoSuc = 0;
    END

    -- 5) Elegir el precio final MÁS BAJO (si ambos existen)
    IF @PrecioReg IS NOT NULL AND @PrecioSuc IS NOT NULL
    BEGIN
        IF @PrecioReg <= @PrecioSuc
        BEGIN
            SET @Precio         = @PrecioReg;
            SET @DescuentoTipo  = ISNULL(@DesctoRegTipo, '');
            SET @Descuento      = ISNULL(@DesctoReg, 0);
            SET @DescuentoMonto = ISNULL(@DesctoRegMonto, 0);
        END
        ELSE
        BEGIN
            SET @Precio         = @PrecioSuc;
            SET @DescuentoTipo  = ISNULL(@DesctoSucTipo, '');
            SET @Descuento      = ISNULL(@DesctoSuc, 0);
            SET @DescuentoMonto = ISNULL(@DesctoSucMonto, 0);
        END
    END
    ELSE IF @PrecioReg IS NOT NULL
    BEGIN
        SET @Precio         = @PrecioReg;
        SET @DescuentoTipo  = ISNULL(@DesctoRegTipo, '');
        SET @Descuento      = ISNULL(@DesctoReg, 0);
        SET @DescuentoMonto = ISNULL(@DesctoRegMonto, 0);
    END
    ELSE IF @PrecioSuc IS NOT NULL
    BEGIN
        SET @Precio         = @PrecioSuc;
        SET @DescuentoTipo  = ISNULL(@DesctoSucTipo, '');
        SET @Descuento      = ISNULL(@DesctoSuc, 0);
        SET @DescuentoMonto = ISNULL(@DesctoSucMonto, 0);
    END
    ELSE
    BEGIN
        SET @Precio         = @PrecioInicial;
        SET @DescuentoTipo  = '';
        SET @Descuento      = 0;
        SET @DescuentoMonto = 0;
    END
END
GO